{% extends "base.html" %}

{% block title %}Espace Enseignant{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='teacher_home.css') }}">
{% endblock %}

{% block content %}
<div class="container">
    <header class="header">
        <div class="header-content">
            <h1>Espace Enseignant</h1>
            <div class="user-info">
                <span class="user-name">{{ user.name }}</span>
                <button class="logout-btn" onclick="showLogoutDialog()">
                    <i class="icon">🚪</i> Déconnexion
                </button>
            </div>
        </div>
    </header>

    <main class="main-content">
        <section class="upload-card">
            <h2>Ajouter un document PDF</h2>
            <form id="pdfUploadForm" method="POST" action="./upload" enctype="multipart/form-data">
                <label for="title">Titre</label>
                <input class="form-row" type="text" name="title" placeholder="Titre du document" required>
                <div class="form-row">
                    <div class="form-group">
                        <label for="classSelect">Classe</label>
                        <select id="classSelect" name="class_id" required>
                            <option value="">Sélectionner une classe</option>
                            <option value="1">1er</option>
                            <option value="2">2éme</option>
                            <option value="3">3éme</option>
                            <option value="4">Baccalauréat</option>
                        </select>
                    </div>

                    <div class="form-group" id="sectionGroup">
                        <label for="subjectSelect">Section</label>
                        <select id="subjectSelect" name="section_id">
                            <option value="">Sélectionner une Section</option>
                            <option value="1">Informatique</option>
                            <option value="2">Mathématiques</option>
                            <option value="3">Sciences expérimentales</option>
                            <option value="4">Economie & gestion</option>
                            <option value="5">Technique</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="pdfFile">Fichier PDF</label>
                    <div class="file-upload-container">
                        <!-- Normal Upload Area (shown when no file selected) -->
                        <div class="file-upload-area" id="dropZone">
                            <div class="upload-icon">📄</div>
                            <p class="upload-text">Glissez-déposez votre fichier ici</p>
                            <p class="upload-subtext">ou</p>
                            <button type="button" class="browse-btn"
                                onclick="document.getElementById('pdfFile').click()">
                                Parcourir les fichiers
                            </button>
                            <input type="file" id="pdfFile" name="pdf_file" accept=".pdf" required
                                onchange="previewFile()">
                        </div>

                        <!-- File Info (shown after file selected) -->
                        <div id="fileInfo" class="file-info" style="display: none;">
                            <div class="file-details">
                                <span class="file-icon">📄</span>
                                <div class="file-text-content">
                                    <span id="fileName" class="file-name"></span>
                                    <span id="fileSize" class="file-size"></span>
                                </div>
                            </div>
                            <button type="button" class="remove-file-btn" onclick="removeFile()">
                                <span class="remove-icon">✕</span>
                            </button>
                        </div>

                        <!-- 🔁 Loading Spinner (hidden by default) -->
                        <div id="loadingSpinner" class="loading-spinner" style="display: none;">
                            <div class="spinner"></div>
                            <span>Upload en cours...</span>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="description">Description (optionnel)</label>
                    <textarea id="description" name="description" rows="3"
                        placeholder="Décrivez le contenu du document..."></textarea>
                </div>

                <button type="submit" class="submit-btn">
                    <i class="icon">📤</i> Ajouter le document
                </button>
            </form>
        </section>

        <section class="info-section">
            <div class="info-card">
                <h3>Informations du compte</h3>
                <div class="info-item">
                    <span class="info-label">Nom:</span>
                    <span class="info-value">{{ user.name }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Email:</span>
                    <span class="info-value">{{ user.email }}</span>
                </div>
            </div>
            <div class="documents-container">
                <div class="documents-header">
                    <h3>📚 My Documents</h3>
                    <div class="search-container">
                        <input type="text" id="documentSearch" placeholder="Search documents..." class="search-input">
                        <span class="clear-search-btn">×</span> 
                        <span class="search-icon">🔍</span>
                    </div>
                </div>

                {% if not grouped_docs %}
                <p class="no-docs">You haven't uploaded any documents yet.</p>
                {% else %}
                <div class="tabs-container">
                    <div id="search-results-container" class="documents-grid" style="display: none;"></div>
                    <div class="tabs-header">
                        {% for class_level in grouped_docs.keys() %}
                        <button class="tab-btn {% if loop.first %}active{% endif %}" data-tab="{{ class_level|replace(' ', '-')|lower }}">
                            {{ class_level }}
                        </button>
                        {% endfor %}
                    </div>

                    <div class="tabs-content">
                        {% for class_level, sections in grouped_docs.items() %}
                        <div class="tab-pane {% if loop.first %}active{% endif %}" id="{{ class_level|replace(' ', '-')|lower }}">
                            <div class="sections-container">
                                {% for section, docs in sections.items() %}
                                <div class="section-accordion">
                                    <div class="section-header">
                                        <h4 class="section-title">
                                            <div class="section-text">
                                                <div class="section-name">Section {{ section }}</div>
                                                <div class="doc-count">{{ docs|length }} document{% if docs|length != 1 %}s{% endif %}</div>
                                            </div>
                                            <span class="toggle-arrow">▼</span>
                                        </h4>
                                    </div>
                                    <div class="section-content">
                                        <div class="documents-grid">
                                            {% for doc in docs %}
                                            <!-- Debug: doc.id = {{ doc.id }} -->
                                            <!-- Debug: url_for('document_page', doc_id=doc.id) = {{ url_for('document_page', doc_id=doc.id) }} -->
                                            <a href="{{ url_for('document_page', doc_id=doc.id) }}" class="document-card-link" 
                                               data-doc-id="doc-{{ doc.id }}"
                                               data-title="{{ (doc.title or doc.filename)|lower }}" 
                                               data-description="{{ (doc.description or '')|lower }}">
                                                <div class="document-card">
                                                    <div class="document-icon">
                                                        <i class="far fa-file-pdf"></i>
                                                    </div>
                                                    <div class="document-details">
                                                        <h4 class="document-title">
                                                            {{ doc.title or doc.filename }}
                                                        </h4>
                                                        {% if doc.description %}
                                                        <p class="document-description">{{ doc.description }}</p>
                                                        {% endif %}
                                                        <div class="document-meta">
                                                            <span class="meta-item">
                                                                <i class="far fa-calendar-alt"></i>
                                                                {{ doc.uploaded_at.strftime('%b %d, %Y') }}
                                                            </span>
                                                            <span class="meta-item">
                                                                <i class="far fa-file"></i>
                                                                {{ "%.2f"|format(doc.file_size/1024) if doc.file_size else '0' }} KB
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </a>
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>
            </div>
        </section>
    </main>
</div>

<!-- Logout Confirmation Dialog (Original Design) -->
<div id="logoutDialog" class="dialog-overlay">
    <div class="dialog-content">
        <h3>Confirmer la déconnexion</h3>
        <p>Êtes-vous sûr de vouloir vous déconnecter ?</p>
        <div class="dialog-actions">
            <form method="POST" action="{{ url_for('logout') }}" style="display: inline;">
                <button type="submit" class="btn-danger">Oui, me déconnecter</button>
            </form>
            <button type="button" class="btn-cancel" onclick="hideLogoutDialog()">Annuler</button>
        </div>
    </div>
</div>

<script>

    // Enhanced file handling with drag and drop
    document.addEventListener('DOMContentLoaded', function () {
        const dropZone = document.getElementById('dropZone');
        const fileInput = document.getElementById('pdfFile');

        // Click on drop zone triggers file input
        dropZone.addEventListener('click', function (e) {
            if (e.target !== fileInput) {
                fileInput.click();
            }
        });

        // Drag and drop events
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, unhighlight, false);
        });

        function highlight() {
            dropZone.classList.add('drag-over');
        }

        function unhighlight() {
            dropZone.classList.remove('drag-over');
        }

        // Handle dropped files
        dropZone.addEventListener('drop', handleDrop, false);

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            handleFiles(files);
        }

        // Handle selected files
        fileInput.addEventListener('change', function () {
            handleFiles(this.files);
        });

        function handleFiles(files) {
            if (files.length > 0) {
                const file = files[0];
                if (file.type === 'application/pdf' || file.name.endsWith('.pdf')) {
                    displayFileInfo(file);
                } else {
                    alert('Veuillez sélectionner un fichier PDF valide.');
                    removeFile();
                }
            }
        }

        function displayFileInfo(file) {
            document.getElementById('fileName').textContent = file.name;
            document.getElementById('fileSize').textContent = formatFileSize(file.size);
            document.getElementById('fileInfo').style.display = 'flex';
            dropZone.style.display = 'none';
        }
    });

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function removeFile() {
        const fileInput = document.getElementById('pdfFile');
        const fileInfo = document.getElementById('fileInfo');
        const dropZone = document.getElementById('dropZone');

        fileInput.value = '';
        fileInfo.style.display = 'none';
        dropZone.style.display = 'block';
    }

    // Existing functions remain unchanged
    function showLogoutDialog() {
        const dialog = document.getElementById('logoutDialog');
        dialog.style.display = 'flex';
        setTimeout(() => {
            dialog.classList.add('show');
        }, 10);
        const firstButton = dialog.querySelector('.btn-danger');
        firstButton.focus();
    }

    function hideLogoutDialog() {
        const dialog = document.getElementById('logoutDialog');
        dialog.classList.remove('show');
        setTimeout(() => {
            dialog.style.display = 'none';
        }, 300);
    }

    document.getElementById('logoutDialog').addEventListener('click', function (e) {
        if (e.target === this) {
            hideLogoutDialog();
        }
    });

    document.addEventListener('keydown', function (e) {
        if (e.key === 'Escape') {
            hideLogoutDialog();
        }
    });

    document.getElementById('logoutDialog').addEventListener('keydown', function (e) {
        if (e.key === 'Tab') {
            const focusableElements = this.querySelectorAll('button');
            const firstElement = focusableElements[0];
            const lastElement = focusableElements[focusableElements.length - 1];

            if (e.shiftKey && document.activeElement === firstElement) {
                e.preventDefault();
                lastElement.focus();
            } else if (!e.shiftKey && document.activeElement === lastElement) {
                e.preventDefault();
                firstElement.focus();
            }
        }
    });

    function updateSubjectVisibility(classId) {
        const subjectSelect = document.getElementById('subjectSelect');
        const subjectGroup = subjectSelect.closest('.form-group');

        // Handle 1er case - hide entire subject section
        if (classId === '1') {
            subjectGroup.style.display = 'none';
            subjectSelect.value = '';
            subjectSelect.removeAttribute('required');
            return;
        }

        // Show subject section for other classes
        subjectGroup.style.display = 'block';
        subjectSelect.setAttribute('required', 'required');

        // Handle 2éme case - hide technique option
        const options = subjectSelect.querySelectorAll('option[value]:not([value=""])');
        options.forEach(option => {
            if (classId === '2' && option.value === '5') {
                // Hide technique for 2éme
                option.style.display = 'none';
            } else {
                // Show all other options
                option.style.display = 'block';
            }
        });

        // Reset selection if currently selected option is now hidden
        const selectedOption = subjectSelect.options[subjectSelect.selectedIndex];
        if (selectedOption && selectedOption.style.display === 'none') {
            subjectSelect.value = '';
        }
    }

    // Add event listener
    document.getElementById('classSelect').addEventListener('change', function () {
        updateSubjectVisibility(this.value);
    });

    // Initialize subject visibility on page load
    const initialClass = document.getElementById('classSelect').value;
    if (initialClass) {
        updateSubjectVisibility(initialClass);
    }

    // Tabs functionality
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabPanes = document.querySelectorAll('.tab-pane');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const tabId = button.getAttribute('data-tab');
            
            // Update active tab
            tabButtons.forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');
            
            // Show corresponding tab content
            tabPanes.forEach(pane => {
                pane.classList.remove('active');
                if (pane.id === tabId) {
                    pane.classList.add('active');
                }
            });
        });
    });

    // Accordion functionality
    const accordionHeaders = document.querySelectorAll('.section-header');
    
    accordionHeaders.forEach(header => {
        header.addEventListener('click', () => {
            const accordion = header.parentElement;
            accordion.classList.toggle('active');
        });
    });

    document.addEventListener('DOMContentLoaded', function () {
        // Real-time search functionality
        const searchInput = document.getElementById('documentSearch');
        if (!searchInput) return; // Exit if search input is not on the page

        const clearSearchBtn = document.querySelector('.clear-search-btn');
        const searchIcon = document.querySelector('.search-icon');

        function toggleSearchIcons() {
            const hasValue = searchInput.value.trim().length > 0;
            clearSearchBtn.style.display = hasValue ? 'inline' : 'none';
            searchIcon.style.display = hasValue ? 'none' : 'inline';
        }

        function performSearch() {
            const searchTerm = searchInput.value.toLowerCase().trim();
            const tabsContent = document.querySelector('.tabs-content');
            const tabsHeader = document.querySelector('.tabs-header');
            const searchResultsContainer = document.getElementById('search-results-container');
            const allDocuments = document.querySelectorAll('.document-card-link');
            const processedDocs = new Set();
            
            // Clear previous results
            searchResultsContainer.innerHTML = '';
            
            if (searchTerm === '') {
                // Show original layout
                tabsContent.style.display = '';
                tabsHeader.style.display = '';
                searchResultsContainer.style.display = 'none';
                // Show all documents and sections
                document.querySelectorAll('.document-card-link').forEach(doc => doc.style.display = '');
                document.querySelectorAll('.section-accordion').forEach(s => s.style.display = '');
                
                // Reset any active tab states if needed
                const activeTab = document.querySelector('.tab-pane.active');
                if (activeTab) {
                    activeTab.style.display = 'block';
                }
                return; // Exit early for empty search
            }
            
            // Hide original layout and show search results
            tabsContent.style.display = 'none';
            tabsHeader.style.display = 'none';
            searchResultsContainer.style.display = 'grid';
            searchResultsContainer.style.gap = '1rem';
            searchResultsContainer.style.padding = '1rem 0';
            
            let matchesFound = false;
            
            allDocuments.forEach(doc => {
                const title = doc.dataset.title?.toLowerCase() || '';
                const description = doc.dataset.description?.toLowerCase() || '';
                const docId = doc.getAttribute('data-doc-id');
                
                // Skip if we've already processed this document
                if (processedDocs.has(docId)) return;
                
                if (title.includes(searchTerm) || description.includes(searchTerm)) {
                    const clone = doc.cloneNode(true);
                    clone.style.display = '';
                    clone.style.width = '100%';
                    searchResultsContainer.appendChild(clone);
                    processedDocs.add(docId);
                    matchesFound = true;
                }
            });
            
            if (!matchesFound) {
                searchResultsContainer.innerHTML = '<p class="no-results-message" style="text-align: center; margin: 20px 0; color: #666; width: 100%;">No matching documents found.</p>';
            }
        }

        searchInput.addEventListener('input', () => {
            toggleSearchIcons();
            performSearch();
        });

        clearSearchBtn.addEventListener('click', () => {
            searchInput.value = '';
            searchInput.focus();
            toggleSearchIcons();
            performSearch();
        });

        // Initial state
        toggleSearchIcons();
    });

    // Open first accordion by default
    const firstAccordion = document.querySelector('.section-accordion');
    if (firstAccordion) {
        firstAccordion.classList.add('active');
    }

    function previewFile() {
        const fileInput = document.getElementById('pdfFile');
        const file = fileInput.files[0];
        if (!file) return;

        // Show file info
        document.getElementById('fileName').textContent = file.name;
        document.getElementById('fileSize').textContent = formatFileSize(file.size);
        document.getElementById('fileInfo').style.display = 'flex';
        document.getElementById('dropZone').style.display = 'none';

        // Attach submit listener to show spinner
        const form = fileInput.closest('form');
        form.onsubmit = function () {
            document.getElementById('loadingSpinner').style.display = 'flex';
            document.getElementById('fileInfo').style.display = 'none';
            document.getElementById('dropZone').style.display = 'none';
            // Disable submit button to prevent double submit
            form.querySelector('button[type="submit"]').disabled = true;
        };
    }

    function removeFile() {
        const fileInput = document.getElementById('pdfFile');
        fileInput.value = '';
        document.getElementById('fileInfo').style.display = 'none';
        document.getElementById('dropZone').style.display = 'flex';
        document.getElementById('loadingSpinner').style.display = 'none';
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

</script>
{% endblock %}