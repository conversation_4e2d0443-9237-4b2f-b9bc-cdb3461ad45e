{% extends "base.html" %}

{% block title %}Connexion{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='login.css') }}">
{% endblock %}

{% block content %}
<div class="login-page-wrapper">
    <div class="login-container">

        <form method="POST" autocomplete="on">
            <!-- logo -->
            <div class="logo">
                <span class="logo-text">Aca</span><span class="logo-accent">demy</span>
            </div>
            <div class="form-group">
                <label for="email">Adresse Email</label>
                <input type="email" id="email" name="email" placeholder="Entrez votre email" required
                    autocomplete="username" maxlength="100">
            </div>

            <div class="form-group">
                <label for="password">Mot de Passe</label>
                <div class="password-container">
                    <input type="password" id="password" name="password" placeholder="Entrez votre mot de passe"
                        required autocomplete="current-password" maxlength="200" minlength="6">
                    <button type="button" class="password-toggle" onclick="togglePassword('password')">
                        👁️
                    </button>
                </div>
            </div>

            <button type="submit">Se Connecter</button>
        </form>


        <div class="google-login-container">
            <div class="separator">
                <span class="separator-text">Ou connectez-vous avec</span>
            </div>

            <a href="{{ url_for('google') }}" class="google-btn modern">
                <div class="google-icon-wrapper">
                    <svg class="google-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48">
                        <path fill="#EA4335"
                            d="M24 9.5c3.54 0 6.71 1.22 9.21 3.6l6.85-6.85C35.9 2.38 30.47 0 24 0 14.62 0 6.51 5.38 2.56 13.22l7.98 6.19C12.43 13.72 17.74 9.5 24 9.5z" />
                        <path fill="#4285F4"
                            d="M46.98 24.55c0-1.57-.15-3.09-.38-4.55H24v9.02h12.94c-.58 2.96-2.26 5.48-4.78 7.18l7.73 6c4.51-4.18 7.09-10.36 7.09-17.65z" />
                        <path fill="#FBBC05"
                            d="M10.53 28.59c-.48-1.45-.76-2.99-.76-4.59s.27-3.14.76-4.59l-7.98-6.19C.92 16.17 0 20.26 0 24.55c0 4.29.92 8.38 2.56 11.81l7.97-6.19z" />
                        <path fill="#34A853"
                            d="M24 48c6.48 0 11.93-2.13 15.89-5.81l-7.73-6c-2.15 1.45-4.92 2.3-8.16 2.3-6.26 0-11.57-4.22-13.49-9.91l-7.98 6.19C6.51 42.62 14.62 48 24 48z" />
                    </svg>
                </div>
                <span class="google-btn-text">Continuer avec Google</span>
                <div class="google-btn-ripple"></div>
            </a>
        </div>

        <div class="signup-link">
            Vous n'avez pas de compte ? <a href="{{ url_for('signup') }}">S'inscrire</a>
        </div>

    </div>
</div>

<script>
    function togglePassword(fieldId) {
        const field = document.getElementById(fieldId);
        const button = field.nextElementSibling;

        if (field.type === 'password') {
            field.type = 'text';
            button.textContent = '🙈';
        } else {
            field.type = 'password';
            button.textContent = '👁️';
        }
    }

</script>
{% endblock %}