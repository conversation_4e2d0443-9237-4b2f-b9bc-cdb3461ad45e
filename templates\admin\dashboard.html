{% extends "base.html" %}

{% block title %}Tableau de Bord Administrateur{% endblock %}

{% block extra_css %}
<style>
    :root {
        --text: #3b1e1e;
        --text-light: #475569;
        --border: #e2e8f0;
        --card-bg: #ffffff;
        --primary: #1e40af;
        --hover: #dbeafe;
        --action-size: 60px;
        --tooltip-bg: #1e293b;
        --tooltip-text: #ffffff;
        --spacing-unit: 0.75rem;
    }

    .admin-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 1.5rem;
    }

    .header {
        margin-bottom: 1.5rem;
    }

    .header h1 {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--text);
        margin: 0;
    }

    .welcome {
        color: var(--text-light);
        font-size: 0.875rem;
    }

    .stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.25rem;
        margin-bottom: 1.75rem;
    }

    .stat {
        padding: 1.25rem 1rem 1.25rem;
        border-radius: 0.75rem;
        background: var(--card-bg);
        border: 1px solid var(--border);
        position: relative;
        overflow: visible;
        text-align: center;
    }

    .stat-title {
        font-size: 0.75rem;
        color: var(--text-light);
        margin: 0 0 0.6rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        font-weight: 500;
    }

    .stat-value {
        font-size: 1.75rem;
        font-weight: 700;
        color: var(--text);
        margin-bottom: 0.5rem;
    }

    .teachers-actions {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        gap: 1rem;
        z-index: 10;
    }

    .action-btn {
        width: var(--action-size);
        height: var(--action-size);
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: var(--text-light);
        background: white;
        border: 2px solid var(--border);
        cursor: pointer;
        transition: all 0.2s cubic-bezier(0.3, 0, 0.2, 1);
        text-decoration: none;
        box-shadow: 0 2px 5px -1px rgba(0, 0, 0, 0.1), 0 1px 3px -1px rgba(0, 0, 0, 0.08);
        position: relative;
        backdrop-filter: blur(1px);
        /* Prevents border artifacts */
    }

    .action-btn:hover {
        background: var(--primary);
        color: white;
        border-color: var(--primary);
        transform: translateY(-3px);
        box-shadow: 0 6px 12px -2px rgba(30, 64, 175, 0.25), 0 4px 6px -2px rgba(30, 64, 175, 0.15);
    }

    .action-btn svg {
        width: 18px;
        height: 18px;
        stroke-width: 2.2;
        transition: transform 0.2s ease;
    }

    .action-btn:hover svg {
        transform: scale(1.1);
    }

    .action-btn::after {
        content: attr(data-tooltip);
        position: absolute;
        bottom: calc(100% + 10px);
        left: 50%;
        transform: translateX(-50%) translateY(8px);
        background: var(--tooltip-bg);
        color: var(--tooltip-text);
        padding: 0.4rem 0.8rem;
        border-radius: 0.5rem;
        font-size: 0.8125rem;
        white-space: nowrap;
        opacity: 0;
        visibility: hidden;
        transition: all 0.2s cubic-bezier(0.3, 0, 0.2, 1);
        pointer-events: none;
        z-index: 20;
        font-weight: 500;
        box-shadow: 0 4px 12px -2px rgba(0, 0, 0, 0.2);
        letter-spacing: 0.01em;
    }

    .action-btn:hover::after {
        opacity: 1;
        visibility: visible;
        transform: translateX(-50%) translateY(0);
    }

    /* Perfect positioning adjustments */
    .stat-title {
        line-height: 1.3;
    }

    .users {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .user {
        padding: 0.75rem 0;
        border-bottom: 1px solid var(--border);
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.875rem;
    }

    .user:last-child {
        border-bottom: none;
    }

    .user-name {
        font-weight: 500;
        color: var(--text);
    }

    .user-email {
        color: var(--text-light);
        margin-top: 0.125rem;
    }

    .role {
        padding: 0.125rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.6875rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .role-admin {
        color: #b91c1c;
        background: #fef2f2;
    }

    .role-teacher {
        color: #15803d;
        background: #f0fdf4;
    }

    .role-student {
        color: #475569;
        background: #f1f5f9;
    }

    .logout {
        text-align: center;
        margin-top: 1.5rem;
    }

    .logout-btn {
        background: transparent;
        /* border: 1px solid var(--border); */
        color: var(--text-light);
        padding: 0.6rem 1.75rem;
        border-radius: 0.5rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: 0.875rem;
    }

    .logout-btn:hover {
        background: var(--hover);
        border-color: var(--primary);
        color: var(--primary);
    }

    /* Dialog styles (copied from static/home.css) */
    .dialog-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.6);
        backdrop-filter: blur(4px);
        z-index: 1000;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .dialog-overlay.show { opacity: 1; }

    .dialog-content {
        background: linear-gradient(145deg, #ffffff, #f8f9fa);
        padding: 40px;
        border-radius: 20px;
        box-shadow:
            0 20px 60px rgba(0, 0, 0, 0.15),
            0 8px 25px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.8);
        text-align: center;
        max-width: 420px;
        margin: 20px;
        transform: scale(0.8) translateY(30px);
        transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        user-select: none;
    }

    .dialog-overlay.show .dialog-content { transform: scale(1) translateY(0); }

    .dialog-content h3 {
        color: #2d3436;
        font-size: 26px;
        margin-bottom: 8px;
        font-weight: 600;
    }

    .dialog-content p {
        color: #636e72;
        font-size: 16px;
        margin-bottom: 30px;
        line-height: 1.6;
        opacity: 0.9;
    }

    .dialog-content::before {
        content: "⚠️";
        font-size: 48px;
        display: block;
        margin-bottom: 20px;
        opacity: 0.8;
        animation: pulse 2s ease-in-out infinite;
    }

    .dialog-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-wrap: wrap;
    }

    .dialog-actions form { margin: 0; padding: 0; background: none; box-shadow: none; }

    .btn-danger {
        background: linear-gradient(135deg, #e17055, #d63031);
        color: white;
        padding: 14px 28px;
        border: none;
        border-radius: 12px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        min-width: 140px;
        box-shadow: 0 4px 15px rgba(225, 112, 85, 0.3);
        position: relative;
        overflow: hidden;
    }

    .btn-danger::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn-danger:hover::before { left: 100%; }

    .btn-danger:hover {
        background: linear-gradient(135deg, #d63031, #c0392b);
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(225, 112, 85, 0.4);
    }

    .btn-cancel {
        background: linear-gradient(135deg, #74b9ff, #0984e3);
        color: white;
        padding: 14px 28px;
        border: none;
        border-radius: 12px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        min-width: 140px;
        box-shadow: 0 4px 15px rgba(116, 185, 255, 0.3);
        position: relative;
        overflow: hidden;
    }

    .btn-cancel::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    .btn-cancel:hover::before { left: 100%; }

    .btn-cancel:hover {
        background: linear-gradient(135deg, #0984e3, #0770c4);
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(116, 185, 255, 0.4);
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }
</style>
{% endblock %}

{% block content %}
<div class="admin-container">
    <div class="header">
        <h1>Tableau de Bord Administrateur</h1>
        <div class="welcome">Bienvenue, {{ current_user.name }}</div>
    </div>

    <div class="stats">
        <div class="stat">
            <div class="stat-title">Utilisateurs</div>
            <div class="stat-value">{{ total_users }}</div>
        </div>

        <div class="stat">
            <div class="stat-title">Enseignants</div>
            <div class="stat-value">{{ total_teachers }}</div>
            <div class="teachers-actions">
                <a href="{{ url_for('admin_teachers') }}" class="action-btn" data-tooltip="Gérer les enseignants">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                        stroke-linecap="round" stroke-linejoin="round">
                        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                        <circle cx="9" cy="7" r="4"></circle>
                        <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                        <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                    </svg>
                </a>
                <a href="{{ url_for('admin_add_teacher') }}" class="action-btn" data-tooltip="Ajouter un enseignant">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                        stroke-linecap="round" stroke-linejoin="round">
                        <line x1="12" y1="5" x2="12" y2="19"></line>
                        <line x1="5" y1="12" x2="19" y2="12"></line>
                    </svg>
                </a>
            </div>
        </div>

        <div class="stat">
            <div class="stat-title">Admins</div>
            <div class="stat-value">{{ total_admins }}</div>
        </div>
    </div>

    <div class="section">
        <div class="section-title">Utilisateurs Récents</div>
        <ul class="users">
            {% if recent_users %}
            {% for user in recent_users %}
            <li class="user">
                <div>
                    <div class="user-name">{{ user.name }}</div>
                    <div class="user-email">{{ user.email }}</div>
                </div>
                <span class="role role-{{ user.get_user_type() }}">{{ user.get_role_display() }}</span>
            </li>
            {% endfor %}
            {% else %}
            <li class="user">Aucun utilisateur récent</li>
            {% endif %}
        </ul>
    </div>

    <div class="logout">
        <button type="button" class="logout-btn" onclick="showLogoutDialog()">Logout</button>
    </div>

    <!-- Logout Confirmation Dialog (same design as home) -->
    <div id="logoutDialog" class="dialog-overlay">
        <div class="dialog-content">
            <h3>Confirmer la déconnexion</h3>
            <p>Êtes-vous sûr de vouloir vous déconnecter ?</p>
            <div class="dialog-actions">
                <form method="POST" action="{{ url_for('admin_logout') }}" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                    <button type="submit" class="btn-danger">Oui, me déconnecter</button>
                </form>
                <button type="button" class="btn-cancel" onclick="hideLogoutDialog()">Annuler</button>
            </div>
        </div>
    </div>
</div>

<script>
    // Popup logic copied from templates/home.html
    function showLogoutDialog() {
        const dialog = document.getElementById('logoutDialog');
        dialog.style.display = 'flex';
        setTimeout(() => { dialog.classList.add('show'); }, 10);
        const firstButton = dialog.querySelector('.btn-danger');
        if (firstButton) firstButton.focus();
    }

    function hideLogoutDialog() {
        const dialog = document.getElementById('logoutDialog');
        dialog.classList.remove('show');
        setTimeout(() => { dialog.style.display = 'none'; }, 300);
    }

    document.getElementById('logoutDialog').addEventListener('click', function (e) {
        if (e.target === this) { hideLogoutDialog(); }
    });

    document.addEventListener('keydown', function (e) {
        if (e.key === 'Escape') { hideLogoutDialog(); }
    });

    document.getElementById('logoutDialog').addEventListener('keydown', function (e) {
        if (e.key === 'Tab') {
            const focusableElements = this.querySelectorAll('button');
            const firstElement = focusableElements[0];
            const lastElement = focusableElements[focusableElements.length - 1];
            if (e.shiftKey && document.activeElement === firstElement) {
                e.preventDefault(); lastElement.focus();
            } else if (!e.shiftKey && document.activeElement === lastElement) {
                e.preventDefault(); firstElement.focus();
            }
        }
    });
</script>
{% endblock %}