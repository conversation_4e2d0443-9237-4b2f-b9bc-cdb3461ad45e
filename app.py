#test PR
#test second one
import os
from flask import Flask, render_template, request, redirect, url_for, flash, session, make_response, abort
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, login_user, logout_user, login_required, current_user
from admin_utils import admin_required, create_admin_session, destroy_admin_session, generate_csrf_token, verify_csrf_token, rate_limit_check
from authlib.integrations.flask_client import OAuth
import logging
from dotenv import load_dotenv
from supabase import create_client
import magic
import datetime  # Added for timestamp
import secrets   # Added for unique filenames
#securefilename import 
from werkzeug.utils import secure_filename
from forms import EditDocumentForm 

load_dotenv()


classes = {1: "1er", 2: "2eme", 3: "3eme", 4: "Baccalaureat"}
sections = {1: "Informatique", 2: "Mathematiques", 3: "Sciences experimentales", 
           4: "Economie & gestion", 5: "Technique"}

app = Flask(__name__)

# Supabase configuration - CRITICAL FIX
SUPABASE_URL = os.environ.get('SUPABASE_URL')
SUPABASE_KEY = os.environ.get('SUPABASE_KEY')  # This is the anon key for database operations
SUPABASE_SERVICE_ROLE_KEY = os.environ.get('SUPABASE_SERVICE_KEY')  # This is the service role key for storage


# Create two separate clients:
# 1. For database operations (uses anon key, respects RLS)
supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
# 2. For storage operations (uses service role key, bypasses RLS)
storage_supabase = create_client(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

# Reduce werkzeug logging
# log = logging.getLogger('werkzeug')
# log.setLevel(logging.WARNING)

# Create Flask app
app = Flask(__name__)
print("\n"*10)
print("#"*40)
print("initialisation de l'application : ")
# Simple configuration
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'dev-secret-key-change-in-production')
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL', 'sqlite:///app.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

from models import db, User, UserRole, PDFDocument

# Initialize extensions
db.init_app(app)

login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'Veuillez vous connecter pour accéder à cette page.'
login_manager.login_message_category = 'info'

#google oauth
oauth = OAuth(app)
google = oauth.register(
    name='google',
    client_id=os.environ.get('GOOGLE_CLIENT_ID'),
    client_secret=os.environ.get('GOOGLE_CLIENT_SECRET'),
    server_metadata_url='https://accounts.google.com/.well-known/openid-configuration  ',
    client_kwargs={
        'scope': 'openid email profile'
    }
)

#just for test do not forget to remove it when finished
##
from utils import create_admin, create_teacher
##

@app.route('/authorize')
def authorize():
    try:
        google = oauth.create_client('google')
        token = google.authorize_access_token()
        
        # Get nonce from session
        nonce = session.pop('oauth_nonce', None)
        
        # Parse ID token with nonce
        user_info = google.parse_id_token(token, nonce=nonce)
        
        # Check if user already exists
        email = user_info.get('email')
        user = User.query.filter_by(email=email).first()
        print(user)
        if not user:
            # Create new user if doesn't exist
            import secrets
            random_password = secrets.token_urlsafe(32)
            user = User.create_user(
                email=email,
                first_name=user_info.get('given_name', ''),
                last_name=user_info.get('family_name', ''),
                password=random_password,
                role=UserRole.STUDENT  # Default role for OAuth users
            )
            db.session.add(user)
            db.session.commit()
            flash('Compte créé avec succès via Google!', 'success')
        else:
            flash('Connexion réussie via Google!', 'success')
        
        # Log in the user
        login_user(user)
        
        if user.role == UserRole.ADMIN:
            # Ensure admin session token is created for protected admin routes
            create_admin_session()
            return redirect(url_for('admin_dashboard'))
        elif user.is_teacher():
            return redirect(url_for('teacher_home'))
        else:
            return student_home()
            
    except Exception as e:
        print(f"OAuth error: {e}")
        flash('Erreur de connexion avec Google. Veuillez réessayer.', 'error')
        return redirect(url_for('login'))

@app.route('/google', endpoint='google')
def google_callback():
    google = oauth.create_client('google')
    redirect_uri = url_for('authorize', _external=True)
    return google.authorize_redirect(redirect_uri)

# Security headers
@app.after_request
def add_security_headers(response):
    """Add security headers to all responses."""
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-XSS-Protection'] = '1; mode=block'
    response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
    response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
    response.headers['Content-Security-Policy'] = "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; font-src 'self' https: data:; img-src 'self' data:;"
    return response

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

def getfiles():
    files=PDFDocument.query.filter_by(class_level=current_user.student_class,section=current_user.section).all()
    print("files:")
    print(files)
    return files
def student_home():
    return redirect(url_for('home', files=getfiles()))

@app.route('/')
def index():
    if current_user.is_authenticated:
        print(current_user.data())
        if current_user.is_teacher():
            return redirect(url_for('teacher_home'))
        else:
            return student_home()
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        if current_user.role == UserRole.ADMIN:
            return redirect(url_for('admin_dashboard'))
        elif current_user.is_teacher():
            return redirect(url_for('teacher_home'))
        else:
            return student_home()

    if request.method == 'POST':
        email = request.form.get('email')
        password = request.form.get('password')

        user = User.query.filter_by(email=email).first()
        if user and user.check_password(password):
            login_user(user)
            if user.role == UserRole.ADMIN:
                # Ensure admin session token is created for protected admin routes
                create_admin_session()
                return redirect(url_for('admin_dashboard'))
            elif user.is_teacher():
                return redirect(url_for('teacher_home'))
            else:
                return student_home()
        else:
            flash('Email ou mot de passe invalide.', 'error')
    return render_template('login.html')

@app.route('/signup', methods=['GET', 'POST'])
def signup():
    if current_user.is_authenticated:
        return student_home()

    if request.method == 'POST':
        first_name = request.form.get('first_name')
        last_name = request.form.get('last_name')
        email = request.form.get('email')
        class_id = int(request.form.get('class_id', 0))
        section_id = request.form.get('section_id')
        if section_id:
            section = sections.get(int(section_id), "")
        else:
            section = None
        Class = classes.get(class_id, "")

        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')

        if not first_name or not last_name or not email or not password or not confirm_password:
            flash('Veuillez remplir tous les champs obligatoires.', 'error')
            return render_template('signup.html')

        if password != confirm_password:
            flash('Les mots de passe ne correspondent pas.', 'error')
            return render_template('signup.html')

        if User.query.filter_by(email=email).first():
            flash('Cet email existe déjà.', 'error')
            return render_template('signup.html')

        user = User.create_user(
            email=email,
            first_name=first_name,
            last_name=last_name,
            password=password,
            role=UserRole.STUDENT,
            student_class=Class,
            section=section
        )
        db.session.add(user)
        db.session.commit()

        login_user(user)
        flash('Compte créé avec succès!', 'success')
        return student_home()

    return render_template('signup.html')


@app.route('/home')
@login_required
def home():
    files=getfiles()
    return render_template('home.html', files=files, user=current_user)

@app.route('/teacher/home')
@login_required
def teacher_home():
    # Get list of documents of this user
    documents = PDFDocument.query.filter_by(teacher_id=current_user.id).all()
    # Group documents: class_level → section → list of docs
    grouped_docs = {}
    for doc in documents:

        class_level = doc.class_level or 'Uncategorized'
        section = doc.section or 'General'  # Use 'General' if no section
        if class_level not in grouped_docs:
            grouped_docs[class_level] = {}
        if section not in grouped_docs[class_level]:
            grouped_docs[class_level][section] = []

        grouped_docs[class_level][section].append(doc)

    # Define classes with subject restrictions
    return render_template('teacher/teacher_home.html', 
                        user=current_user,
                        grouped_docs=grouped_docs,
                        )

@app.route('/teacher/upload', methods=['GET'])
@login_required
def show_upload_form():
    return render_template('teacher/teacher_home.html', user=current_user)

@app.route('/teacher/upload', methods=['POST'])
@login_required
def handle_upload():
    if 'pdf_file' not in request.files:
        return "No file part", 400
    file = request.files['pdf_file']
    if file.filename == '':
        return "No file selected", 400

    if file and file.filename.endswith('.pdf'):
        # Secure the original filename for display
        original_filename = secure_filename(file.filename)
        
        title = request.form.get('title', 'Untitled')
        class_id = int(request.form.get('class_id', 0))
        section_id = request.form.get('section_id')
        description = request.form.get('description', '')

        Class = classes.get(class_id, "")
        section = sections.get(int(section_id), "") if section_id else None

        # Generate unique filename for storage (timestamp + random hex)
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        random_str = secrets.token_hex(4)  # 8 hex characters
        unique_filename = f"{timestamp}_{random_str}.pdf"

        # Build remote path in Supabase (e.g., uploads/1er/Informatique/report.pdf)
        path_parts = ['uploads' ]
        remote_path = '/'.join(path_parts + [unique_filename])

        # --- UPLOAD TO SUPABASE STORAGE - USING SERVICE ROLE KEY ---
        try:
            # Read file into memory
            file_stream = file.stream.read()

            # Detect MIME type (critical security step)
            mime = magic.from_buffer(file_stream, mime=True)
            if mime != "application/pdf":
                return "Only PDF files are allowed", 400

            # Prepare file options with content-disposition for proper downloads
            # Escape double quotes in filename for header safety
            safe_filename = original_filename.replace('"', '\\"')
            file_options = {
                "content-type": "application/pdf",
                "content-disposition": f'attachment; filename="{safe_filename}"'
            }

            # Upload to Supabase with unique name - USING storage_supabase (service role)
            storage_supabase.storage.from_("documents").upload(
                path=remote_path,
                file=file_stream,
                file_options=file_options
            )

            # Get public URL - USING storage_supabase (service role)
            public_url = storage_supabase.storage.from_("documents").get_public_url(remote_path)

        except Exception as e:
            print("Supabase upload error:", str(e))
            return "Upload to cloud failed. Try again.", 500

        # --- SAVE METADATA TO DATABASE ---
        pdf_doc = PDFDocument(
            title=title,
            filename=original_filename,  # Store original name for display
            class_level=Class,
            section=section,
            teacher_id=current_user.id,
            file_size=len(file_stream),
            description=description,
            file_url=public_url  # Save the public link
        )
        db.session.add(pdf_doc)
        db.session.commit()

        flash('Fichier téléchargé avec succès!', 'success')
        return redirect('/teacher/home')

    return "Invalid file type. Only PDFs allowed.", 400

@app.route('/document/<string:doc_id>', endpoint='document_page')
@login_required
def document_page(doc_id):
    doc = PDFDocument.query.get_or_404(doc_id)

    # Security check: ensure the current user is the owner of the document
    if doc.teacher_id != current_user.id:
        abort(403) # Forbidden access

    return render_template('teacher/document.html', document=doc)

@app.route('/document/<string:doc_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_document(doc_id):
    doc = PDFDocument.query.get_or_404(doc_id)

    if doc.teacher_id != current_user.id:
        abort(403)

    form = EditDocumentForm(obj=doc)

    if form.validate_on_submit():
        doc.title = form.title.data
        doc.description = form.description.data
        doc.class_level = form.class_level.data
        doc.section = form.section.data if form.class_level.data != '1er' else None
        db.session.commit()
        flash('Document details updated successfully!', 'success')
        return redirect(url_for('document_page', doc_id=doc.id))

    return render_template('teacher/edit_document.html', form=form, document=doc)
@app.route('/logout', methods=['POST'])
@login_required
def logout():
    logout_user()
    return redirect(url_for('login'))


# Admin Routes
@app.route('/admin/login', methods=['GET', 'POST'])
@login_required
def admin_login():
    """Admin login disabled."""
    abort(404)


@app.route('/admin/')
@admin_required
def admin_dashboard():
    """Secure admin dashboard."""
    # Get statistics
    total_users = User.query.count()
    total_teachers = User.query.filter_by(role=UserRole.TEACHER).count()
    total_admins = User.query.filter_by(role=UserRole.ADMIN).count()
    recent_users = User.query.order_by(User.created_at.desc()).limit(5).all()
    print(total_users, total_teachers, total_admins, recent_users)
    return render_template('admin/dashboard.html',
                         total_users=total_users,
                         total_teachers=total_teachers,
                         total_admins=total_admins,
                         recent_users=recent_users,
                         csrf_token=generate_csrf_token())


@app.route('/admin/logout', methods=['POST'])
@admin_required
def admin_logout():
    """Logout from admin session."""
    # Verify CSRF token for logout action
    csrf_token = request.form.get('csrf_token')
    if not verify_csrf_token(csrf_token):
        flash('Token de sécurité invalide.', 'error')
        return redirect(url_for('admin_dashboard'))

    destroy_admin_session()
    # Also end the full user login session
    logout_user()
    flash('Déconnexion administrateur réussie.', 'success')
    return redirect(url_for('login'))


# Teacher Management Routes
@app.route('/admin/teachers')
@admin_required
def admin_teachers():
    """View all teachers."""
    teachers = User.query.filter_by(role=UserRole.TEACHER).order_by(User.created_at.desc()).all()
    return render_template('admin/teachers.html',
                         teachers=teachers,
                         csrf_token=generate_csrf_token())


@app.route('/admin/teachers/add', methods=['GET', 'POST'])
@admin_required
def admin_add_teacher():
    """Add a new teacher."""
    if request.method == 'POST':
        # Verify CSRF token
        csrf_token = request.form.get('csrf_token')
        if not verify_csrf_token(csrf_token):
            flash('Token de sécurité invalide.', 'error')
            return render_template('admin/add_teacher.html', csrf_token=generate_csrf_token())

        # Get form data
        first_name = request.form.get('first_name', '').strip()
        last_name = request.form.get('last_name', '').strip()
        email = request.form.get('email', '').strip().lower()
        password = request.form.get('password', '')
        confirm_password = request.form.get('confirm_password', '')

        # Validation
        errors = []
        if not first_name:
            errors.append('Le prénom est requis.')
        if not last_name:
            errors.append('Le nom de famille est requis.')
        if not email:
            errors.append('L\'email est requis.')
        if not password:
            errors.append('Le mot de passe est requis.')
        if password != confirm_password:
            errors.append('Les mots de passe ne correspondent pas.')
        if len(password) < 6:
            errors.append('Le mot de passe doit contenir au moins 6 caractères.')

        # Check if email already exists
        if User.query.filter_by(email=email).first():
            errors.append('Cet email existe déjà.')

        if errors:
            for error in errors:
                flash(error, 'error')
            return render_template('admin/add_teacher.html',
                                 csrf_token=generate_csrf_token(),
                                 first_name=first_name,
                                 last_name=last_name,
                                 email=email)

        # Create teacher
        teacher = User.create_user(
            email=email,
            first_name=first_name,
            last_name=last_name,
            password=password,
            role=UserRole.TEACHER
        )

        try:
            db.session.add(teacher)
            db.session.commit()
            flash(f'Enseignant {teacher.name} ajouté avec succès.', 'success')
            return redirect(url_for('admin_teachers'))
        except Exception as e:
            db.session.rollback()
            flash('Erreur lors de l\'ajout de l\'enseignant.', 'error')

    return render_template('admin/add_teacher.html', csrf_token=generate_csrf_token())


@app.route('/admin/teachers/<int:teacher_id>/edit', methods=['GET', 'POST'])
@admin_required
def admin_edit_teacher(teacher_id):
    """Edit a teacher."""
    teacher = User.query.filter_by(id=teacher_id, role=UserRole.TEACHER).first_or_404()

    if request.method == 'POST':
        # Verify CSRF token
        csrf_token = request.form.get('csrf_token')
        if not verify_csrf_token(csrf_token):
            flash('Token de sécurité invalide.', 'error')
            return render_template('admin/edit_teacher.html',
                                 teacher=teacher,
                                 csrf_token=generate_csrf_token())

        # Get form data
        first_name = request.form.get('first_name', '').strip()
        last_name = request.form.get('last_name', '').strip()
        email = request.form.get('email', '').strip().lower()
        is_active = request.form.get('is_active') == 'on'
        new_password = request.form.get('new_password', '')

        # Validation
        errors = []
        if not first_name:
            errors.append('Le prénom est requis.')
        if not last_name:
            errors.append('Le nom de famille est requis.')
        if not email:
            errors.append('L\'email est requis.')

        # Check if email already exists (excluding current teacher)
        existing_user = User.query.filter_by(email=email).first()
        if existing_user and existing_user.id != teacher.id:
            errors.append('Cet email existe déjà.')

        if new_password and len(new_password) < 6:
            errors.append('Le nouveau mot de passe doit contenir au moins 6 caractères.')

        if errors:
            for error in errors:
                flash(error, 'error')
            return render_template('admin/edit_teacher.html',
                                 teacher=teacher,
                                 csrf_token=generate_csrf_token())

        # Update teacher
        teacher.first_name = first_name
        teacher.last_name = last_name
        teacher.email = email
        teacher.is_active = is_active

        if new_password:
            teacher.set_password(new_password)

        try:
            db.session.commit()
            flash(f'Enseignant {teacher.name} modifié avec succès.', 'success')
            return redirect(url_for('admin_teachers'))
        except Exception as e:
            db.session.rollback()
            flash('Erreur lors de la modification de l\'enseignant.', 'error')

    return render_template('admin/edit_teacher.html',
                         teacher=teacher,
                         csrf_token=generate_csrf_token())


@app.route('/admin/teachers/<int:teacher_id>/delete', methods=['POST'])
@admin_required
def admin_delete_teacher(teacher_id):
    """Delete a teacher."""
    teacher = User.query.filter_by(id=teacher_id, role=UserRole.TEACHER).first_or_404()

    # Verify CSRF token
    csrf_token = request.form.get('csrf_token')
    if not verify_csrf_token(csrf_token):
        flash('Token de sécurité invalide.', 'error')
        return redirect(url_for('admin_teachers'))

    try:
        teacher_name = teacher.name
        db.session.delete(teacher)
        db.session.commit()
        flash(f'Enseignant {teacher_name} supprimé avec succès.', 'success')
    except Exception as e:
        db.session.rollback()
        flash('Erreur lors de la suppression de l\'enseignant.', 'error')

    return redirect(url_for('admin_teachers'))



if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        create_admin("Admin", "Admin", "<EMAIL>", "123456")
        create_teacher("Teacher", "Teacher", "<EMAIL>", "123456")
    app.run(debug=True)
