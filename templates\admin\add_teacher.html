<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ajouter un Enseignant - Administration</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: #f8fafc;
            color: #334155;
            line-height: 1.6;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 32px 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 32px;
        }

        .header h1 {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
        }

        .header p {
            color: #64748b;
            font-size: 16px;
        }

        .form-card {
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e2e8f0;
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #374151;
            font-size: 14px;
        }

        .required {
            color: #f97316;
            font-weight: 500;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.2s ease;
            background: white;
        }

        .form-input:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .form-input:invalid:not(:placeholder-shown) {
            border-color: #f97316;
        }

        .form-input.error {
            border-color: #f97316;
        }

        .form-input.success {
            border-color: #10b981;
        }

        .password-hint {
            background: #f0f9ff;
            border: 1px solid #e0f2fe;
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
        }

        .password-hint h4 {
            font-size: 14px;
            font-weight: 600;
            color: #0369a1;
            margin-bottom: 8px;
        }

        .password-hint ul {
            font-size: 13px;
            color: #0c4a6e;
            list-style: none;
        }

        .password-hint li {
            margin-bottom: 4px;
            padding-left: 16px;
            position: relative;
        }

        .password-hint li:before {
            content: "•";
            position: absolute;
            left: 0;
            color: #0369a1;
        }

        .form-actions {
            display: flex;
            gap: 12px;
            margin-top: 32px;
            padding-top: 24px;
            border-top: 1px solid #e5e7eb;
        }

        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            font-size: 14px;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #2563eb;
            color: white;
            flex: 1;
            justify-content: center;
        }

        .btn-primary:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: white;
            color: #6b7280;
            border: 1px solid #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
            color: #4b5563;
            text-decoration: none;
        }



        @media (max-width: 768px) {
            .container {
                padding: 20px 16px;
            }

            .form-card {
                padding: 24px;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .form-actions {
                flex-direction: column;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }
        }

        /* Modern checkbox styling for future use */
        .checkbox {
            appearance: none;
            width: 20px;
            height: 20px;
            border: 2px solid #d1d5db;
            border-radius: 4px;
            position: relative;
            cursor: pointer;
        }

        .checkbox:checked {
            background: #2563eb;
            border-color: #2563eb;
        }

        .checkbox:checked:after {
            content: "✓";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
            font-weight: bold;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>Ajouter un Enseignant</h1>
            <p>Créer un nouveau compte enseignant</p>
        </div>

        <div class="form-card">
            <form method="POST">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">

                <div class="form-row">
                    <div class="form-group">
                        <label for="first_name">
                            Prénom <span class="required">*</span>
                        </label>
                        <input type="text" id="first_name" name="first_name" class="form-input"
                            value="{{ first_name or '' }}" required autocomplete="given-name" placeholder="Prénom">
                    </div>

                    <div class="form-group">
                        <label for="last_name">
                            Nom <span class="required">*</span>
                        </label>
                        <input type="text" id="last_name" name="last_name" class="form-input"
                            value="{{ last_name or '' }}" required autocomplete="family-name" placeholder="Nom">
                    </div>
                </div>

                <div class="form-group">
                    <label for="email">
                        Adresse email <span class="required">*</span>
                    </label>
                    <input type="email" id="email" name="email" class="form-input" value="{{ email or '' }}" required
                        autocomplete="email" placeholder="<EMAIL>">
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="password">
                            Mot de passe <span class="required">*</span>
                        </label>
                        <input type="password" id="password" name="password" class="form-input" required
                            autocomplete="new-password" minlength="8" placeholder="••••••••">
                    </div>

                    <div class="form-group">
                        <label for="confirm_password">
                            Confirmer <span class="required">*</span>
                        </label>
                        <input type="password" id="confirm_password" name="confirm_password" class="form-input" required
                            autocomplete="new-password" minlength="8" placeholder="••••••••">
                    </div>
                </div>

                <div class="password-hint">
                    <h4>Exigences du mot de passe</h4>
                    <ul>
                        <li>Minimum 8 caractères</li>
                        <li>Mélange de lettres, chiffres et symboles recommandé</li>
                        <li>Évitez les mots de passe communs</li>
                    </ul>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <span>Créer l'Enseignant</span>
                    </button>
                    <a href="{{ url_for('admin_teachers') }}" class="btn btn-secondary">
                        Annuler
                    </a>
                </div>
            </form>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const password = document.getElementById('password');
            const confirmPassword = document.getElementById('confirm_password');

            function validatePasswords() {
                if (password.value && confirmPassword.value) {
                    if (password.value !== confirmPassword.value) {
                        confirmPassword.setCustomValidity('Les mots de passe ne correspondent pas');
                    } else {
                        confirmPassword.setCustomValidity('');
                    }
                }
            }

            password.addEventListener('input', validatePasswords);
            confirmPassword.addEventListener('input', validatePasswords);

            // Enhanced form validation feedback
            const inputs = document.querySelectorAll('.form-input');
            inputs.forEach(input => {
                input.addEventListener('blur', function () {
                    // Only show validation colors after user has interacted and moved away
                    if (this.value && !this.checkValidity()) {
                        this.classList.add('error');
                        this.classList.remove('success');
                    } else if (this.value && this.checkValidity()) {
                        this.classList.add('success');
                        this.classList.remove('error');
                    }
                });

                input.addEventListener('input', function () {
                    // Remove validation classes while typing
                    this.classList.remove('error', 'success');
                });
            });
        });
    </script>
</body>

</html>