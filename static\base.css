/* Base styles for all pages */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
    min-height: 100vh;
}

/* Flash messages */
.flash-messages {
    max-width: 600px;
    margin: 20px auto;
    padding: 0 20px;
}

.flash-message {
    padding: 12px 16px;
    margin-bottom: 10px;
    border-radius: 4px;
    font-weight: 500;
}

.flash-message.error {
    background-color: #fee;
    color: #c33;
    border: 1px solid #fcc;
}

.flash-message.success {
    background-color: #efe;
    color: #363;
    border: 1px solid #cfc;
}

.flash-message.info {
    background-color: #eef;
    color: #336;
    border: 1px solid #ccf;
}

/* Container */
.container {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
}

/* Typography */
h1 {
    color: #2c3e50;
    margin-bottom: 20px;
    text-align: center;
}

h2 {
    color: #34495e;
    margin-bottom: 15px;
}

p {
    margin-bottom: 15px;
    color: #555;
}

/* Links */
a {
    color: #3498db;
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: #2980b9;
    text-decoration: underline;
}

/* Buttons */
button, .btn {
    background-color: #3498db;
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s ease;
    display: inline-block;
    text-decoration: none;
}

button:hover, .btn:hover {
    background-color: #2980b9;
}

button[type="submit"] {
    background-color: #27ae60;
}

button[type="submit"]:hover {
    background-color: #229954;
}

/* Forms */
form {
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

input[type="text"],
input[type="email"],
input[type="password"] {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

small {
    color: #777;
    font-size: 14px;
}
