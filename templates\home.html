{% extends "base.html" %}

{% block title %}Accueil - Tableau de bord{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link rel="stylesheet" href="{{ url_for('static', filename='home.css') }}">
{% endblock %}

{% block content %}
<div class="home-container fade-in">
    <!-- Hero Section -->
    <section class="hero">
        <h1>Bienvenue sur votre espace</h1>
        <p class="welcome-message">
            Bonjour <span class="highlight">{{ user.name }}</span>, vous êtes connecté avec succès à votre compte.
        </p>
        <div class="action-buttons">
            <button type="button" onclick="showLogoutDialog()" class="btn btn-outline">
                <i class="fas fa-sign-out-alt"></i> Déconnexion
            </button>
        </div>
    </section>

    <!-- Files Section -->
    <section class="files-section">
        <h2 class="section-title">Vos fichiers</h2>
        
        {% if files %}
        <div class="files-grid">
            {% for file in files %}
            <div class="file-card">
                <div class="file-icon">
                    <i class="far fa-file-alt"></i>
                </div>
                <h3 class="file-name">{{ file.title }}</h3>
                <a href="{{ file.file_url }}" class="file-link">
                    Ouvrir le fichier
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </a>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="empty-state">
            <i class="far fa-folder-open" style="font-size: 3rem; color: var(--gray); margin-bottom: 1rem;"></i>
            <p>Vous n'avez pas encore de fichiers.</p>
        </div>
        {% endif %}
    </section>
</div>

<!-- Logout Confirmation Dialog -->
<div id="logoutDialog" class="dialog-overlay">
    <div class="dialog-content">
        <div class="dialog-header">
            <i class="fas fa-sign-out-alt dialog-icon"></i>
            <h3>Confirmer la déconnexion</h3>
        </div>
        <p>Êtes-vous sûr de vouloir vous déconnecter ?</p>
        <div class="dialog-actions">
            <button type="button" class="btn btn-outline" onclick="hideLogoutDialog()">
                Annuler
            </button>
            <form method="POST" action="{{ url_for('logout') }}" style="display: inline;">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-sign-out-alt"></i> Se déconnecter
                </button>
            </form>
        </div>
    </div>
</div>

<style>
.dialog-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.dialog-overlay.show {
    display: flex;
    opacity: 1;
}

.dialog-content {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    max-width: 450px;
    width: 90%;
    transform: translateY(20px);
    transition: transform 0.3s ease;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.dialog-overlay.show .dialog-content {
    transform: translateY(0);
}

.dialog-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.dialog-icon {
    font-size: 1.5rem;
    color: var(--accent);
    margin-right: 0.75rem;
}

.dialog-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 1.5rem;
}

.highlight {
    color: var(--primary);
    font-weight: 600;
}
</style>

<script>
function showLogoutDialog() {
    const dialog = document.getElementById('logoutDialog');
    dialog.style.display = 'flex';
    
    // Trigger animation
    setTimeout(() => {
        dialog.classList.add('show');
        document.body.style.overflow = 'hidden'; // Prevent scrolling when dialog is open
    }, 10);

    // Focus trap for accessibility
    const firstButton = dialog.querySelector('.btn');
    firstButton.focus();
}

function hideLogoutDialog() {
    const dialog = document.getElementById('logoutDialog');
    dialog.classList.remove('show');
    document.body.style.overflow = ''; // Re-enable scrolling

    // Hide after animation completes
    setTimeout(() => {
        dialog.style.display = 'none';
    }, 300);
}

// Close dialog when clicking outside
document.getElementById('logoutDialog').addEventListener('click', function (e) {
    if (e.target === this) {
        hideLogoutDialog();
    }
});

// Close dialog with Escape key
document.addEventListener('keydown', function (e) {
    if (e.key === 'Escape') {
        hideLogoutDialog();
    }
});

// Keyboard navigation within dialog
document.getElementById('logoutDialog').addEventListener('keydown', function (e) {
    if (e.key === 'Tab') {
        const focusableElements = this.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        if (e.shiftKey && document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
        } else if (!e.shiftKey && document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
        }
    }
});
</script>
{% endblock %}