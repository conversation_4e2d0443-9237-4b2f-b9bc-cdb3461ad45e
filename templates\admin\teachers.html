{% extends "base.html" %}
{% block title %}Gestion des Enseignants - Administration{% endblock %}
{% block extra_css %}
<style>
    /* Modern Reset & Typography */
    *,
    *::before,
    *::after {
        box-sizing: border-box;
    }

    body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        line-height: 1.6;
        color: #333;
        background: #f7fafc;
        padding: 0;
        margin: 0;
    }

    /* Layout */
    .admin-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 24px 16px;
    }

    .admin-header {
        background: linear-gradient(135deg, #2c3e50, #4361ee);
        color: white;
        padding: 24px 32px;
        border-radius: 12px;
        margin-bottom: 28px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-shadow: 0 4px 16px rgba(44, 62, 80, 0.15);
    }

    .admin-header h1 {
        color: white;
        /* Soft dark gray (modern alternative to black) */
        font-size: 1.5rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .header-actions {
        display: flex;
        gap: 12px;
    }

    /* Buttons */
    .btn {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 10px 16px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 600;
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        min-height: 38px;
    }

    .btn-primary {
        background: #007bff;
        color: white;
    }

    .btn-primary:hover {
        background: #0069d9;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.2);
    }

    .btn-secondary {
        background: #6c757d;
        color: white;
    }

    .btn-secondary:hover {
        background: #5a6268;
        transform: translateY(-1px);
    }

    .btn-outline {
        background: transparent;
        color: #007bff;
        border: 1px solid #007bff;
    }

    .btn-outline:hover {
        background: #007bff;
        color: white;
    }

    .btn-sm {
        padding: 6px 10px;
        font-size: 13px;
    }

    .btn-danger {
        background: #dc3545;
        color: white;
    }

    .btn-danger:hover {
        background: #c82333;
        transform: translateY(-1px);
    }

    /* Section Card */
    .teachers-section {
        background: white;
        border-radius: 16px;
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.06);
        overflow: hidden;
    }

    .teachers-section h2 {
        padding: 24px 24px 16px;
        font-size: 18px;
        font-weight: 600;
        color: #2d3748;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    /* Table */
    .teachers-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 14px;
    }

    .teachers-table th,
    .teachers-table td {
        padding: 14px 16px;
        text-align: left;
        border-bottom: 1px solid #edf2f7;
    }

    .teachers-table th {
        background: #f8fafc;
        color: #4a5568;
        font-weight: 600;
        font-size: 12px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        padding-top: 12px;
        padding-bottom: 12px;
    }

    .teachers-table tbody tr {
        transition: background 0.2s ease;
    }

    .teachers-table tbody tr:hover {
        background-color: #f8fafc;
    }

    .teachers-table td {
        color: #4a5568;
    }

    .teachers-table .actions-cell {
        width: 120px;
    }

    /* Action Buttons */
    .action-buttons {
        display: flex;
        gap: 8px;
    }

    /* No Teachers */
    .no-teachers {
        padding: 60px 24px;
        text-align: center;
        color: #718096;
    }

    .no-teachers .icon {
        font-size: 60px;
        margin-bottom: 16px;
        color: #e2e8f0;
    }

    .no-teachers h3 {
        font-size: 18px;
        font-weight: 600;
        color: #4a5568;
        margin-bottom: 8px;
    }

    .no-teachers p {
        margin-bottom: 16px;
        color: #718096;
        font-size: 14px;
    }

    /* Delete Trigger */
    .delete-trigger {
        background: none;
        border: none;
        padding: 6px 10px;
        border-radius: 6px;
        cursor: pointer;
        color: #e53e3e;
        font-size: 13px;
        transition: all 0.2s ease;
    }

    .delete-trigger:hover {
        background: #e53e3e;
        color: white;
    }

    /* Modal */
    .modal {
        position: fixed;
        z-index: 1000;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.4);
        display: flex;
        align-items: center;
        justify-content: center;
        animation: fadeIn 0.3s ease;
    }

    .modal-content {
        background: white;
        border-radius: 16px;
        width: 90%;
        max-width: 480px;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
        overflow: hidden;
    }

    .modal-header {
        padding: 20px 24px;
        border-bottom: 1px solid #edf2f7;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .modal-header h2 {
        font-size: 18px;
        font-weight: 600;
        color: #2d3748;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .modal-body {
        padding: 24px;
        color: #4a5568;
        font-size: 14px;
    }

    .modal-footer {
        padding: 16px 24px;
        border-top: 1px solid #edf2f7;
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        background: #f8fafc;
    }

    .close {
        font-size: 22px;
        font-weight: 300;
        color: #a0aec0;
        cursor: pointer;
    }

    .close:hover {
        color: #2d3748;
    }

    .warning-text {
        color: #e53e3e;
        font-weight: 600;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
        }

        to {
            opacity: 1;
        }
    }

    /* Responsive */
    @media (max-width: 768px) {
        .admin-header {
            flex-direction: column;
            gap: 16px;
            text-align: center;
        }

        .header-actions {
            width: 100%;
            justify-content: center;
        }

        .teachers-table th,
        .teachers-table td {
            padding: 12px 8px;
            font-size: 13px;
        }

        .teachers-table .actions-cell {
            width: 110px;
        }

        .action-buttons {
            flex-wrap: nowrap;
        }

        .admin-container {
            padding: 16px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="admin-container">
    <!-- Header -->
    <header class="admin-header">
        <h1>👨‍🏫 Gestion des Enseignants</h1>
        <div class="header-actions">
            <a href="{{ url_for('admin_add_teacher') }}" class="btn btn-primary">
                ➕ Ajouter
            </a>
            <a href="{{ url_for('admin_dashboard') }}" class="btn btn-secondary">
                ← Retour
            </a>
        </div>
    </header>

    <!-- Teachers Section -->
    <section class="teachers-section">
        <h2>📋 Liste des Enseignants ({{ teachers|length }})</h2>

        {% if teachers %}
        <table class="teachers-table">
            <thead>
                <tr>
                    <th>Nom</th>
                    <th>Email</th>
                    <th>Inscrit le</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for teacher in teachers %}
                <tr>
                    <td><strong>{{ teacher.name }}</strong></td>
                    <td>{{ teacher.email }}</td>
                    <td>{{ teacher.created_at.strftime('%d/%m/%Y') }}</td>
                    <td class="actions-cell">
                        <div class="action-buttons">
                            <a href="{{ url_for('admin_edit_teacher', teacher_id=teacher.id) }}"
                                class="btn btn-outline btn-sm" title="Modifier">
                                ✏️
                            </a>
                            <button type="button" class="btn delete-trigger btn-sm" data-teacher-id="{{ teacher.id }}"
                                data-teacher-name="{{ teacher.name }}" title="Supprimer">
                                🗑️
                            </button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <!-- Empty State -->
        <div class="no-teachers">
            <div class="icon">🏫</div>
            <h3>Aucun enseignant trouvé</h3>
            <p>Commencez par ajouter votre premier enseignant.</p>
            <a href="{{ url_for('admin_add_teacher') }}" class="btn btn-primary">
                ➕ Ajouter un Enseignant
            </a>
        </div>
        {% endif %}
    </section>
</div>

<!-- Delete Confirmation Modal (will be injected by JS) -->
<div id="deleteModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h2>⚠️ Confirmer la suppression</h2>
            <span class="close">&times;</span>
        </div>
        <div class="modal-body">
            Êtes-vous sûr de vouloir supprimer l'enseignant <strong class="warning-text" id="teacherName"></strong> ?
            <p style="margin-top: 12px; font-size: 13px; color: #718096;">
                Cette action est irréversible.
            </p>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" id="cancelDelete">Annuler</button>
            <button type="button" class="btn btn-danger" id="confirmDelete">Supprimer</button>
        </div>
    </div>
</div>

<script>
    // Simple modal logic for delete confirmation
    document.addEventListener("DOMContentLoaded", () => {
        const modal = document.getElementById("deleteModal");
        const closeBtn = modal.querySelector(".close");
        const cancelBtn = document.getElementById("cancelDelete");
        const confirmBtn = document.getElementById("confirmDelete");
        let currentTeacherId = null;

        // Open modal
        document.querySelectorAll(".delete-trigger").forEach(button => {
            button.addEventListener("click", () => {
                currentTeacherId = button.dataset.teacherId;
                document.getElementById("teacherName").textContent = button.dataset.teacherName;
                modal.style.display = "flex";
            });
        });

        // Close modal
        [closeBtn, cancelBtn].forEach(el => {
            el.addEventListener("click", () => {
                modal.style.display = "none";
            });
        });

        // Confirm delete (you can replace with form submit)
        confirmBtn.addEventListener("click", () => {
            const form = document.createElement("form");
            form.method = "POST";
            form.action = `/admin/teachers/delete/${currentTeacherId}`;
            document.body.appendChild(form);
            form.submit();
        });

        // Close on outside click
        window.addEventListener("click", (e) => {
            if (e.target === modal) {
                modal.style.display = "none";
            }
        });
    });
</script>
{% endblock %}