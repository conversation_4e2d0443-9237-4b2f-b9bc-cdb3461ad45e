/* Modern Home Page Styles */
:root {
    --primary: #4361ee;
    --primary-light: #4895ef;
    --secondary: #3f37c9;
    --accent: #f72585;
    --light: #f8f9fa;
    --dark: #212529;
    --gray: #6c757d;
    --border-radius: 12px;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

body {
    background: linear-gradient(135deg, #f5f7ff 0%, #e9ecff 100%);
    min-height: 100vh;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    color: var(--dark);
    line-height: 1.6;
}

.home-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1.5rem;
}

/* Hero Section */
.hero {
    text-align: center;
    padding: 4rem 2rem;
    margin-bottom: 3rem;
    border-radius: var(--border-radius);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.8) 100%);
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow);
    border: 1px solid rgba(255, 255, 255, 0.5);
}

.hero h1 {
    font-size: 2.8rem;
    font-weight: 800;
    color: var(--primary);
    margin-bottom: 1rem;
    background: linear-gradient(90deg, var(--primary), var(--secondary));
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.hero p {
    font-size: 1.2rem;
    color: var(--gray);
    max-width: 700px;
    margin: 0 auto 2rem;
}

/* Files Section */
.files-section {
    margin-top: 3rem;
}

.section-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--dark);
    margin-bottom: 1.5rem;
    position: relative;
    display: inline-block;
}

.section-title::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -8px;
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary), var(--accent));
    border-radius: 2px;
}

.files-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.file-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow);
    transition: var(--transition);
    border: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
}

.file-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.file-icon {
    font-size: 2.5rem;
    color: var(--primary);
    margin-bottom: 1rem;
}

.file-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--dark);
    margin-bottom: 0.5rem;
    word-break: break-word;
}

.file-link {
    display: inline-flex;
    align-items: center;
    color: var(--primary);
    text-decoration: none;
    font-weight: 500;
    margin-top: auto;
    padding-top: 1rem;
    transition: var(--transition);
}

.file-link:hover {
    color: var(--secondary);
    text-decoration: underline;
}

.file-link svg {
    margin-left: 0.5rem;
    transition: var(--transition);
}

.file-link:hover svg {
    transform: translateX(3px);
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    border: none;
    font-size: 1rem;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary), var(--secondary));
    color: white;
    box-shadow: 0 4px 15px rgba(67, 97, 238, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(67, 97, 238, 0.4);
    color: white;
}

.btn-outline {
    background: transparent;
    color: var(--primary);
    border: 2px solid var(--primary);
}

.btn-outline:hover {
    background: rgba(67, 97, 238, 0.05);
    transform: translateY(-2px);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 2rem;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-top: 2rem;
}

.empty-state p {
    color: var(--gray);
    margin-bottom: 1.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero {
        padding: 3rem 1.5rem;
    }
    
    .hero h1 {
        font-size: 2.2rem;
    }
    
    .files-grid {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .btn {
        width: 100%;
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.6s ease-out forwards;
}

/* Loading State */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(67, 97, 238, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Utility Classes */
.text-center { text-align: center; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }
.file-section a {
    color: var(--primary);
    text-decoration: none;
    font-weight: 500;
    display: flex;
    align-items: center;
    padding: 0.5rem 0;
    transition: color 0.2s ease;
}

.file-section a:hover {
    color: var(--secondary);
    text-decoration: underline;
}

/* Optional: Add a small icon before file links */
.file-section a::before {
    content: "📄";
    margin-right: 0.75rem;
    opacity: 0.8;
}

/* Responsive design */
@media (max-width: 600px) {
    .file-section {
        margin: 1rem;
        padding: 1.5rem;
    }

    .file-section h1 {
        font-size: 1.5rem;
    }

    .file-section a {
        font-size: 0.95rem;
    }
}


/* Dialog styles */
.dialog-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    z-index: 1000;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.dialog-overlay.show {
    opacity: 1;
}

.dialog-content {
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    padding: 40px;
    border-radius: 20px;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.15),
        0 8px 25px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    text-align: center;
    max-width: 420px;
    margin: 20px;
    transform: scale(0.8) translateY(30px);
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.dialog-overlay.show .dialog-content {
    transform: scale(1) translateY(0);
}

.dialog-content h3 {
    color: #2d3436;
    font-size: 26px;
    margin-bottom: 8px;
    font-weight: 600;
}

.dialog-content p {
    color: #636e72;
    font-size: 16px;
    margin-bottom: 30px;
    line-height: 1.6;
    opacity: 0.9;
}

.dialog-content::before {
    content: "⚠️";
    font-size: 48px;
    display: block;
    margin-bottom: 20px;
    opacity: 0.8;
}

.dialog-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-danger {
    background: linear-gradient(135deg, #e17055, #d63031);
    color: white;
    padding: 14px 28px;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 140px;
    box-shadow: 0 4px 15px rgba(225, 112, 85, 0.3);
    position: relative;
    overflow: hidden;
}

.btn-danger::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-danger:hover::before {
    left: 100%;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #d63031, #c0392b);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(225, 112, 85, 0.4);
}

.btn-cancel {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    color: white;
    padding: 14px 28px;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 140px;
    box-shadow: 0 4px 15px rgba(116, 185, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.btn-cancel::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-cancel:hover::before {
    left: 100%;
}

.btn-cancel:hover {
    background: linear-gradient(135deg, #0984e3, #0770c4);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(116, 185, 255, 0.4);
}

/* Responsive dialog */
@media (max-width: 500px) {
    .dialog-content {
        margin: 20px 15px;
        padding: 35px 25px;
        border-radius: 16px;
    }

    .dialog-content::before {
        font-size: 40px;
        margin-bottom: 15px;
    }

    .dialog-content h3 {
        font-size: 22px;
    }

    .dialog-actions {
        flex-direction: column;
        align-items: center;
        gap: 12px;
    }

    .btn-danger,
    .btn-cancel {
        width: 100%;
        max-width: 220px;
        padding: 16px 24px;
    }
}

/* Additional polish */
.dialog-content {
    user-select: none;
}

.dialog-actions form {
    margin: 0;
    padding: 0;
    background: none;
    box-shadow: none;
}

/* Subtle pulse animation for warning icon */
@keyframes pulse {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }
}

.dialog-content::before {
    animation: pulse 2s ease-in-out infinite;
}