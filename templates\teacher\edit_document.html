{% extends "base.html" %}

{% block title %}Edit Document{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='form.css') }}">
{% endblock %}

{% block content %}
<div class="form-container">
    <header class="form-header">
        <h1>Edit Document Details</h1>
    </header>

    <form method="POST" action="">
        {{ form.hidden_tag() }}
        <div class="form-body">
            <div class="form-group">
                {{ form.title.label }}
                {{ form.title(class="form-control") }}
            </div>

            <div class="form-group">
                {{ form.description.label }}
                {{ form.description(class="form-control") }}
            </div>

            <div class="form-group">
                {{ form.class_level.label }}
                {{ form.class_level(class="form-control", id="class_level") }}
            </div>

            <div class="form-group" id="section-group">
                {{ form.section.label }}
                {{ form.section(class="form-control") }}
            </div>
        </div>
        <footer class="form-footer">
            <a href="{{ url_for('document_page', doc_id=document.id) }}" class="btn btn-secondary">Cancel</a>
            {{ form.submit(class="btn btn-primary") }}
        </footer>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const classLevelSelect = document.getElementById('class_level');
    const sectionGroup = document.getElementById('section-group');

    function toggleSectionField() {
        if (classLevelSelect.value !== '1er') {
            sectionGroup.style.display = 'block';
        } else {
            sectionGroup.style.display = 'none';
        }
    }

    // Run on page load
    toggleSectionField();

    // Run on selection change
    classLevelSelect.addEventListener('change', toggleSectionField);
});
</script>
{% endblock %}
