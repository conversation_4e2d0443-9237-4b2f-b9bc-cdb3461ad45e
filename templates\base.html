<!DOCTYPE html>
<html lang="fr">
<style>
    .logo {
        font-size: 48px;
        font-weight: 700;
        letter-spacing: 1px;
        text-align: center;
    }

    .logo-text {
        color: #333;
    }

    .logo-accent {
        color: #007BFF;
        text-shadow: 2px 2px 4px rgba(0, 123, 255, 0.3);
    }
</style>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="robots" content="noindex, nofollow">
    <meta name="referrer" content="strict-origin-when-cross-origin">
        <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; font-src 'self' https: data:; img-src 'self' data:;">
    <title>{% block title %}Application Simple{% endblock %}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='base.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="shortcut icon" href="{{ url_for('static', filename='favicon.ico') }}">
    {% block extra_css %}{% endblock %}
</head>

<body>
    {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
    <div class="flash-messages">
        {% for category, message in messages %}
        <div class="flash-message {{ category }}">{{ message }}</div>
        {% endfor %}
    </div>
    {% endif %}
    {% endwith %}

    {% block content %}{% endblock %}
</body>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Select flash messages (adjust selectors based on your actual classes)
        const flashMessages = document.querySelectorAll('.flash-message, .alert, .alert-success, .alert-error, .alert-warning, .alert-info');
        // Auto-dismiss each message after 5 seconds
        flashMessages.forEach(function (message) {
            setTimeout(function () {
                // Fade out effect
                message.style.transition = 'opacity 0.3s ease';
                message.style.opacity = '0';

                // Remove element after fade out completes
                setTimeout(function () {
                    if (message.parentNode) {
                        message.parentNode.removeChild(message);
                    }
                }, 300);
            }, 1000);
        });
    });
</script>

</html>