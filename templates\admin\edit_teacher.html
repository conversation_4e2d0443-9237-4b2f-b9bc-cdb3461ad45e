{% extends "base.html" %}

{% block title %}Modifier l'Enseignant - Administration{% endblock %}

{% block extra_css %}
<style>
    .admin-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .admin-header {
        background: linear-gradient(135deg, #ffc107, #e0a800);
        color: #212529;
        padding: 30px;
        border-radius: 10px;
        margin-bottom: 30px;
        text-align: center;
    }
    
    .admin-header h1 {
        margin: 0;
        font-size: 28px;
    }
    
    .teacher-info {
        background: #e7f3ff;
        border: 1px solid #b3d9ff;
        padding: 20px;
        border-radius: 5px;
        margin-bottom: 30px;
    }
    
    .teacher-info h3 {
        margin: 0 0 10px 0;
        color: #0056b3;
    }
    
    .form-section {
        background: white;
        padding: 40px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .form-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 20px;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-group.full-width {
        grid-column: 1 / -1;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: bold;
        color: #333;
    }
    
    .form-group label .required {
        color: #dc3545;
    }
    
    .form-group input[type="text"],
    .form-group input[type="email"],
    .form-group input[type="password"] {
        width: 100%;
        padding: 12px;
        border: 2px solid #ddd;
        border-radius: 5px;
        font-size: 16px;
        box-sizing: border-box;
        transition: border-color 0.3s;
    }
    
    .form-group input:focus {
        outline: none;
        border-color: #ffc107;
    }
    
    .checkbox-group {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 5px;
        border: 2px solid #ddd;
    }
    
    .checkbox-group input[type="checkbox"] {
        width: auto;
        margin: 0;
    }
    
    .checkbox-group label {
        margin: 0;
        font-weight: normal;
        cursor: pointer;
    }
    
    .password-section {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        padding: 20px;
        border-radius: 5px;
        margin: 20px 0;
    }
    
    .password-section h4 {
        margin: 0 0 15px 0;
        color: #856404;
    }
    
    .password-note {
        font-size: 14px;
        color: #856404;
        margin-bottom: 15px;
        font-style: italic;
    }
    
    .form-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #eee;
    }
    
    .btn {
        padding: 12px 30px;
        border-radius: 5px;
        text-decoration: none;
        font-weight: bold;
        transition: all 0.3s;
        border: none;
        cursor: pointer;
        font-size: 16px;
    }
    
    .btn-warning {
        background: #ffc107;
        color: #212529;
    }
    
    .btn-warning:hover {
        background: #e0a800;
    }
    
    .btn-secondary {
        background: #6c757d;
        color: white;
    }
    
    .btn-secondary:hover {
        background: #545b62;
        color: white;
        text-decoration: none;
    }
    
    .security-notice {
        background: #d1ecf1;
        border: 1px solid #bee5eb;
        color: #0c5460;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
        font-size: 14px;
    }
    
    @media (max-width: 768px) {
        .form-grid {
            grid-template-columns: 1fr;
        }
        
        .form-actions {
            flex-direction: column;
        }
        
        .admin-container {
            padding: 10px;
        }
        
        .form-section {
            padding: 20px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="admin-container">
    <div class="admin-header">
        <h1>✏️ Modifier l'Enseignant</h1>
    </div>
    
    <div class="teacher-info">
        <h3>👨‍🏫 Enseignant actuel :</h3>
        <p><strong>{{ teacher.name }}</strong> - {{ teacher.email }}</p>
        <p>Inscrit le : {{ teacher.created_at.strftime('%d/%m/%Y à %H:%M') }}</p>
    </div>
    
    <div class="form-section">
        <div class="security-notice">
            <strong>🔒 Sécurité :</strong> Toutes les modifications seront enregistrées de manière sécurisée. 
            Laissez le mot de passe vide si vous ne souhaitez pas le modifier.
        </div>
        
        <form method="POST">
            <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
            
            <div class="form-grid">
                <div class="form-group">
                    <label for="first_name">
                        Prénom <span class="required">*</span>
                    </label>
                    <input type="text" 
                           id="first_name" 
                           name="first_name" 
                           value="{{ teacher.first_name }}"
                           required 
                           autocomplete="given-name">
                </div>
                
                <div class="form-group">
                    <label for="last_name">
                        Nom de famille <span class="required">*</span>
                    </label>
                    <input type="text" 
                           id="last_name" 
                           name="last_name" 
                           value="{{ teacher.last_name }}"
                           required 
                           autocomplete="family-name">
                </div>
            </div>
            
            <div class="form-group">
                <label for="email">
                    Adresse email <span class="required">*</span>
                </label>
                <input type="email" 
                       id="email" 
                       name="email" 
                       value="{{ teacher.email }}"
                       required 
                       autocomplete="email">
            </div>
            
            <div class="form-group">
                <label for="is_active">Statut du compte :</label>
                <div class="checkbox-group">
                    <input type="checkbox" 
                           id="is_active" 
                           name="is_active" 
                           {% if teacher.is_active %}checked{% endif %}>
                    <label for="is_active">Compte actif (l'enseignant peut se connecter)</label>
                </div>
            </div>
            
            <div class="password-section">
                <h4>🔐 Modification du mot de passe</h4>
                <div class="password-note">
                    Laissez ce champ vide si vous ne souhaitez pas modifier le mot de passe actuel.
                </div>
                
                <div class="form-group">
                    <label for="new_password">Nouveau mot de passe :</label>
                    <input type="password" 
                           id="new_password" 
                           name="new_password" 
                           autocomplete="new-password"
                           minlength="8"
                           placeholder="Nouveau mot de passe (optionnel)">
                </div>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-warning">
                    ✅ Enregistrer les Modifications
                </button>
                <a href="{{ url_for('admin_teachers') }}" class="btn btn-secondary">
                    ❌ Annuler
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}
