from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, TextAreaField, SelectField, SubmitField
from wtforms.validators import DataRequired, Length

class EditDocumentForm(FlaskForm):
    title = StringField('Title', validators=[DataRequired(), Length(min=3, max=100)])
    description = TextAreaField('Description', validators=[Length(max=500)])
    class_level = SelectField('Class Level', choices=[
        ('1er', '1er'),
        ('2eme', '2ème'),
        ('3eme', '3ème'),
        ('Baccalaureat', 'Baccalauréat')
    ], validators=[DataRequired()])
    section = SelectField('Section', choices=[
        ('', '---'),
        ('Informatique', 'Informatique'),
        ('Mathematiques', 'Mathématiques'),
        ('Sciences experimentales', 'Sciences Expérimentales'),
        ('Economie & gestion', 'Économie & Gestion'),
        ('Technique', 'Technique')
    ])
    submit = SubmitField('Save Changes')
