/* Login page specific styles */
body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px 0;
}

.login-page-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    flex-direction: column;
}

.login-container {
    background: white;
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
    margin: 20px;
}

.login-container h1 {
    color: #333;
    margin-bottom: 10px;
    font-size: 28px;
}

.login-container p {
    color: #666;
    margin-bottom: 30px;
    text-align: center;
}

.password-container {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    font-size: 18px;
}

.signup-link {
    text-align: center;
    margin-top: 20px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 6px;
}

.signup-link a {
    font-weight: 500;
}

/* Form adjustments for login */
form {
    margin-bottom: 0;
    box-shadow: none;
    padding: 0;
}

input[type="email"],
input[type="password"],
input[type="text"] {
    padding: 15px;
    font-size: 16px;
    border: 2px solid #e1e5e9;
}

button[type="submit"] {
    width: 100%;
    padding: 15px;
    font-size: 16px;
    font-weight: 600;
    margin-top: 10px;
}

/* Flash messages on login page */
.flash-messages {
    max-width: 400px;
    margin: 0 auto 20px;
    padding: 0 20px;
}

.flash-message {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    padding: 16px 20px;
    margin-bottom: 15px;
    font-weight: 500;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    animation: slideInFromTop 0.5s ease-out;
}

.flash-message.info {
    background: rgba(116, 185, 255, 0.95);
    color: white;
    border-color: rgba(255, 255, 255, 0.3);
}

.flash-message.error {
    background: rgba(225, 112, 85, 0.95);
    color: white;
    border-color: rgba(255, 255, 255, 0.3);
}

.flash-message.success {
    background: rgba(39, 174, 96, 0.95);
    color: white;
    border-color: rgba(255, 255, 255, 0.3);
}

@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}


/* Google button modernisé - Ajouts à votre CSS existant */
.google-login-container {
    margin: 24px 0;
    width: 100%;
}

.separator {
    display: flex;
    align-items: center;
    text-align: center;
    margin: 20px 0;
}

.separator::before,
.separator::after {
    content: '';
    flex: 1;
    border-bottom: 1px solid #e0e0e0;
}

.separator::before {
    margin-right: 16px;
}

.separator::after {
    margin-left: 16px;
}

.separator-text {
    color: #757575;
    font-size: 0.875rem;
    font-weight: 500;
}

.google-btn.modern {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    max-width: 400px;
    height: 52px;
    background-color: #fff;
    border: 1px solid #dadce0;
    border-radius: 12px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
    padding: 0 24px;
    color: #3c4043;
    font-family: 'Google Sans', Roboto, Arial, sans-serif;
    font-size: 15px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    margin: 0 auto;
}

.google-btn.modern:hover {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
    border-color: #bdc1c6;
    transform: translateY(-1px);
}

.google-btn.modern:active {
    transform: translateY(0);
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.08);
}


.google-icon-wrapper {
    width: 24px;
    height: 24px;
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.google-icon {
    width: 100%;
    height: 100%;
}

.google-btn-text {
    flex: 1;
    text-align: center;
}

.google-btn-ripple {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0);
    width: 100px;
    height: 100px;
    background-color: rgba(66, 133, 244, 0.15);
    border-radius: 50%;
    pointer-events: none;
    transition: transform 0.6s, opacity 1s;
}

.google-btn.modern:active .google-btn-ripple {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
    transition: transform 0s, opacity 0s;
}

/* Responsive adjustments */
@media (max-width: 480px) {
    .google-btn.modern {
        height: 48px;
        font-size: 14px;
        border-radius: 10px;
    }

    .google-icon-wrapper {
        width: 20px;
        height: 20px;
        margin-right: 10px;
    }
}