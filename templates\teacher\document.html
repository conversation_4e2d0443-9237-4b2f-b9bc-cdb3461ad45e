{% extends "base.html" %}

{% block title %}{{ document.to_dict().get('title', 'Document') }} - Details{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='document.css') }}">
{% endblock %}

{% block content %}
<div class="document-container">
    <header class="doc-header">
        <h1 class="doc-title">{{ document.to_dict().get('title', 'No Title') }}</h1>
        <p class="doc-meta">Uploaded on {{ (document.to_dict().get('uploaded_at') or '').split('T')[0] or 'N/A' }}</p>
    </header>

    <div class="doc-body">
        <main class="doc-description">
            <h3>Description</h3>
            <p>{{ document.to_dict().get('description') or 'No description provided.' }}</p>
        </main>
        <aside class="doc-details-card">
            <h3>Details</h3>
            <div class="info-grid">
                <div class="info-item">
                    <span class="label">Class:</span>
                    <span class="value">{{ document.to_dict().get('class_level', 'N/A') }}</span>
                </div>
                {% if document.to_dict().get('class_level') != '1er' %}
                <div class="info-item">
                    <span class="label">Section:</span>
                    <span class="value">{{ document.to_dict().get('section') or 'N/A' }}</span>
                </div>
                {% endif %}
                <div class="info-item">
                    <span class="label">File Name:</span>
                    <span class="value">{{ document.to_dict().get('filename', 'Unknown') }}</span>
                </div>
                <div class="info-item">
                    <span class="label">File Size:</span>
                    <span class="value">{{ document.to_dict().get('file_size', 'N/A') }}</span>
                </div>
            </div>
        </aside>
    </div>

    <footer class="doc-actions">
        <a href="{{ url_for('teacher_home') }}" class="btn btn-secondary">Back to List</a>
        <a href="{{ url_for('edit_document', doc_id=document.id) }}" class="btn btn-primary">Edit Details</a>
    </footer>
</div>
{% endblock %}