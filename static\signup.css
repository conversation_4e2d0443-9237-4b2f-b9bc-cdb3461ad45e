/* Signup page specific styles */
body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px 0;
    min-height: 100vh;
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

.signup-container {
    background: white;
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    max-width: 500px;
    margin: 0 auto;
}

.signup-container h1 {
    color: #333;
    margin-bottom: 10px;
    font-size: 28px;
    text-align: center;
}

.signup-container p {
    color: #666;
    margin-bottom: 30px;
    text-align: center;
}

/* Form structure */
form {
    margin-bottom: 0;
    box-shadow: none;
    padding: 0;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #34495e;
    font-size: 14px;
}

/* Form row for side-by-side inputs */
.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

/* Input styles */
input[type="text"],
input[type="email"],
input[type="password"] {
    width: 100%;
    padding: 15px;
    font-size: 16px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    background: #fafafa;
    transition: border-color 0.2s, box-shadow 0.2s;
    box-sizing: border-box;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus {
    border-color: #3498db;
    outline: none;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    background: white;
}

/* Select dropdown styles */
.form-group select {
    width: 100%;
    padding: 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    background: #fafafa;
    transition: border-color 0.2s, box-shadow 0.2s;
    box-sizing: border-box;
}

.form-group select:focus {
    border-color: #3498db;
    outline: none;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    background: white;
}

/* Password container and toggle */
.password-container {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    font-size: 18px;
    color: #666;
    z-index: 1;
}

.password-toggle:hover {
    color: #333;
}

/* Submit button */
button[type="submit"] {
    width: 100%;
    padding: 15px;
    font-size: 16px;
    font-weight: 600;
    margin-top: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;
}

button[type="submit"]:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

button[type="submit"]:active {
    transform: translateY(0);
}

/* Small text/helper text */
small {
    display: block;
    margin-top: 5px;
    color: #666;
    font-size: 13px;
}

/* Login link section */
.login-link {
    text-align: center;
    margin-top: 20px;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.login-link a {
    font-weight: 500;
    color: #667eea;
    text-decoration: none;
}

.login-link a:hover {
    text-decoration: underline;
}

/* Flash messages */
.flash-messages {
    max-width: 500px;
    margin: 0 auto 20px;
    padding: 0 20px;
}

.flash-message {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    padding: 16px 20px;
    margin-bottom: 15px;
    font-weight: 500;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    animation: slideInFromTop 0.5s ease-out;
}

.flash-message.info {
    background: rgba(116, 185, 255, 0.95);
    color: white;
    border-color: rgba(255, 255, 255, 0.3);
}

.flash-message.error {
    background: rgba(225, 112, 85, 0.95);
    color: white;
    border-color: rgba(255, 255, 255, 0.3);
}

.flash-message.success {
    background: rgba(39, 174, 96, 0.95);
    color: white;
    border-color: rgba(255, 255, 255, 0.3);
}

@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive design */
@media (max-width: 600px) {
    body {
        padding: 10px 0;
    }

    .form-row {
        flex-direction: column;
        gap: 0;
    }

    .form-row .form-group {
        margin-bottom: 20px;
    }

    .signup-container {
        margin: 0 20px;
        padding: 30px 25px;
    }

    .flash-messages {
        padding: 0 10px;
    }
}