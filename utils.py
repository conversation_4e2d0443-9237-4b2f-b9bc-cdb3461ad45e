
from models import db, User,Teacher, Admin, User<PERSON><PERSON>
def create_admin(first_name, last_name, email, password):

    if Admin.query.filter_by(email=email).first():
        print(f"⚠️  Un administrateur existe déjà : {email}")
        return False
    
    
    admin_user = Admin(
            first_name=first_name,
            last_name=last_name,
            email=email,
        )
    admin_user.set_password(password)
    
    try:
        db.session.add(admin_user)
        db.session.commit()
        print("\n✅ Administrateur créé avec succès !")
    except Exception as e:
        db.session.rollback()
        print(f"❌ Erreur lors de la création de l'administrateur : {e}")
        return False
    
    return True

def create_teacher(first_name, last_name, email, password):
    
    if User.query.filter_by(email=email).first():
        print(f"⚠️  Un enseignant existe déjà : {email}")
        return False
    # Create teacher
    teacher = User.create_user(
        email=email,
        first_name=first_name,
        last_name=last_name,
        password=password,
        role=UserRole.TEACHER
    )

    try:
        db.session.add(teacher)
        db.session.commit()
        print("\n✅ Enseignant créé avec succès !")
    except Exception as e:
        db.session.rollback()
        print(f"❌ Erreur lors de la création de l'enseignant : {e}")
        return False
    return True
