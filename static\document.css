:root {
    --primary-color: #4a90e2;
    --background-color: #f8f9fa;
    --surface-color: #ffffff;
    --text-primary: #333;
    --text-secondary: #777;
    --border-color: #dee2e6;
}

.document-container {
    max-width: 900px;
    margin: 2rem auto;
    background-color: var(--surface-color);
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    overflow: hidden;
}

.doc-header {
    padding: 2rem;
    border-bottom: 1px solid var(--border-color);
}

.doc-title {
    font-size: 2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.doc-meta {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-top: 0.5rem;
}

.doc-body {
    padding: 2rem;
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
}

.doc-description h3 {
    font-size: 1.2rem;
    margin-top: 0;
    color: var(--text-primary);
}

.doc-description p {
    line-height: 1.6;
    color: var(--text-secondary);
}

.doc-details-card {
    background-color: var(--background-color);
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
}

.doc-details-card h3 {
    font-size: 1.1rem;
    margin-top: 0;
    color: var(--text-primary);
}

.info-grid {
    display: grid;
    gap: 1rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
}

.info-item .label {
    color: var(--text-secondary);
}

.info-item .value {
    font-weight: 500;
    color: var(--text-primary);
}

.doc-actions {
    padding: 1.5rem 2rem;
    background-color: var(--background-color);
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

.btn {
    padding: 0.6rem 1.2rem;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    border: 1px solid transparent;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}
.btn-primary:hover {
    opacity: 0.9;
}

.btn-secondary {
    background-color: var(--surface-color);
    color: var(--text-primary);
    border-color: var(--border-color);
}
.btn-secondary:hover {
    background-color: var(--background-color);
}