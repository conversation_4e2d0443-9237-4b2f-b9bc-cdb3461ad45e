/* ===== Base Styles ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
    background-color: #f5f7fa;
    color: #333;
    line-height: 1.6;
    margin: 0;
    padding: 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* ===== Header Styles ===== */
.header {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    padding: 20px;
    margin-bottom: 24px;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

.header h1 {
    font-size: 24px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 16px;
}

.user-name {
    font-weight: 500;
    color: #34495e;
}

.logout-btn {
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 10px 16px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background 0.2s;
}

.logout-btn:hover {
    background: #c0392b;
}

/* ===== Main Content Layout ===== */
.main-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 24px;
}

/* ===== Upload Card ===== */
.upload-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    padding: 28px;
}

.upload-card h2 {
    font-size: 20px;
    font-weight: 600;
    color: #2c3e50;
    margin-top: 0;
    margin-bottom: 24px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #34495e;
    font-size: 14px;
}

.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 15px;
    background: #fafafa;
    transition: border-color 0.2s;
}

.form-group select:focus,
.form-group textarea:focus {
    border-color: #3498db;
    outline: none;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-group textarea {
    width: 100%;
    max-width: 100%;
    min-width: 100%;
    height: 120px;
    min-height: 80px;
    max-height: 300px;
    box-sizing: border-box;
}

/* ===== File Upload Redesigned ===== */
.file-upload-container {
    width: 100%;
}

.file-upload-area {
    border: 2px dashed #3498db;
    border-radius: 12px;
    padding: 30px 20px;
    text-align: center;
    background-color: #f8fbff;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: #2980b9;
    background-color: #edf7ff;
}

.upload-icon {
    font-size: 48px;
    margin-bottom: 15px;
    color: #3498db;
}

.upload-text {
    font-size: 16px;
    color: #2c3e50;
    margin: 0 0 10px;
    font-weight: 500;
}

.upload-subtext {
    color: #7f8c8d;
    margin: 5px 0;
    font-size: 14px;
}

.browse-btn {
    background: #3498db;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-size: 15px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    margin-top: 10px;
}

.browse-btn:hover {
    background: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.25);
}

#pdfFile {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
}

/* ===== File Info Display ===== */
.file-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #e8f4ff;
    border-radius: 12px;
    border: 1px solid #b3d9ff;
    margin-top: 15px;
}

.file-details {
    display: flex;
    align-items: center;
    gap: 15px;
    flex: 1;
}

.file-icon {
    font-size: 24px;
    color: #3498db;
}

.file-text-content {
    display: flex;
    flex-direction: column;
    min-width: 0;
}

.file-name {
    font-size: 15px;
    font-weight: 500;
    color: #2c3e50;
    word-break: break-word;
    margin-bottom: 3px;
}

.file-size {
    font-size: 13px;
    color: #7f8c8d;
}

.remove-file-btn {
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 8px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
}

.remove-file-btn:hover {
    background: #c0392b;
    transform: scale(1.1);
}

.remove-icon {
    font-size: 18px;
    font-weight: bold;
}

/* ===== Loading Spinner (Integrated Style) ===== */
.loading-spinner {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 16px 20px;
    background: #e8f4ff;
    border: 1px solid #b3d9ff;
    border-radius: 12px;
    margin-top: 15px;
    color: #2c3e50;
    font-size: 15px;
    font-weight: 500;
    text-align: center;
}

.loading-spinner .spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #b3d9ff;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Optional: Slight pulse effect on text */
.loading-spinner span {
    opacity: 0.9;
    animation: fadeText 2s infinite;
}

@keyframes fadeText {

    0%,
    100% {
        opacity: 0.9;
    }

    50% {
        opacity: 1;
    }
}

/* ===== Submit Button ===== */
.submit-btn {
    background: #27ae60;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 14px 20px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    transition: background 0.2s;
}

.submit-btn:hover {
    background: #219653;
}


/* ===== Documents Container ===== */
.documents-container {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    padding: 16px;
    margin-top: 16px;
}

.documents-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    flex-wrap: wrap;
    gap: 12px;
}

.search-container {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    max-width: 320px;
}

.search-input {
    width: 100%;
    padding: 8px 30px 8px 12px; /* Right padding for icons */
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.search-input:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    outline: none;
}

.search-icon, .clear-search-btn {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #aaa;
    transition: color 0.2s;
    font-size: 16px;
    -webkit-user-select: none; /* Safari */
    -ms-user-select: none; /* IE 10+ */
    user-select: none; /* Standard */
}

.clear-search-btn {
    font-size: 20px;
    color: #999;
    display: none; /* Initially hidden */
}

.clear-search-btn:hover {
    color: #333;
}

.search-icon {
    pointer-events: none; /* The search icon itself isn't clickable */
}

/* Tabs */
.tabs-header {
    display: flex;
    gap: 6px;
    border-bottom: 1px solid #e1e4e8;
    margin-bottom: 16px;
    flex-wrap: wrap;
    padding-bottom: 2px;
}

.tab-btn {
    padding: 6px 14px;
    background: none;
    border: none;
    border-bottom: 2px solid transparent;
    font-size: 13px;
    font-weight: 500;
    color: #576574;
    cursor: pointer;
    transition: all 0.2s;
    border-radius: 4px 4px 0 0;
    margin-bottom: -1px;
}

.tab-btn:hover {
    color: #2c3e50;
    background-color: #f8f9fa;
}

.tab-btn.active {
    color: #3498db;
    border-bottom-color: #3498db;
    font-weight: 600;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

/* Accordion Sections */
.section-accordion {
    margin-bottom: 12px;
    border: 1px solid #e1e4e8;
    border-radius: 6px;
    overflow: hidden;
}

.section-header {
    background-color: #f5f7fa;
    border-radius: 6px;
    transition: all 0.2s;
    margin-bottom: 4px;
}

.section-header:hover {
    background-color: #edf2f7;
}

.section-header:hover {
    background-color: #f1f5f9;
}

.section-title {
    margin: 0;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 10px 12px;
    cursor: pointer;
    user-select: none;
    position: relative;
    padding-right: 30px; /* Make space for the arrow */
}

.section-text {
    flex-grow: 1;
    min-width: 0;
}

.section-name {
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Toggle arrow */
.toggle-arrow {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 16px;
    color: #000;
    transition: transform 0.3s ease-in-out;
    font-weight: 900;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f0f0f0;
    border-radius: 3px;
}

.section-accordion.active .toggle-arrow {
    transform: rotate(180deg);
}

.doc-count {
    font-size: 11px;
    color: #6c757d;
    margin-top: 2px;
    white-space: nowrap;
}

.section-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out;
}

.section-accordion.active .section-content {
    max-height: 5000px; /* Adjust based on your content */
    transition: max-height 0.5s ease-in;
}

/* ===== Documents Grid ===== */
.documents-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
    gap: 10px;
    padding: 6px;
}

.document-card-link {
    text-decoration: none;
    color: inherit;
    display: block;
    transition: transform 0.2s;
}

.document-card-link:hover .document-card {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #3498db;
}

.document-card {
    background: white;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    padding: 10px;
    display: flex;
    gap: 10px;
    transition: transform 0.2s, box-shadow 0.2s, border-color 0.2s;
    border: 1px solid #e1e4e8;
    height: 100%;
    box-sizing: border-box;
}

.document-icon {
    font-size: 20px;
    color: #e74c3c;
    margin-top: 2px;
    flex-shrink: 0;
}

.document-details {
    flex: 1;
    min-width: 0;
}

.document-title {
    color: #2c3e50;
    text-decoration: none;
    transition: color 0.2s;
    font-size: 13px;
    font-weight: 600;
    margin: 0 0 4px 0;
    line-height: 1.3;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.document-card-link:hover .document-title {
    color: #3498db;
}

.document-description {
    font-size: 12px;
    color: #6c757d;
    margin: 0 0 6px 0;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    /* Standard property for line-clamp */
    line-clamp: 2;
    max-height: 2.6em;
}

.document-meta {
    display: flex;
    gap: 10px;
    font-size: 10px;
    color: #868e96;
    margin-top: 2px;
font-size: 10px;
color: #868e96;
margin-top: 2px;
}

.meta-item {
display: flex;
align-items: center;
gap: 3px;
}

.meta-item i {
font-size: 10px;
opacity: 0.7;
}

/* Class and Section Headers */
.class-group {
margin-bottom: 24px;
}

.class-title {
    font-size: 18px;
    color: #2c3e50;
    margin: 0 0 12px 0;
    padding-bottom: 8px;
    border-bottom: 2px solid #f1f3f5;
}

.section-group {
    margin-bottom: 20px;
    background: #f8fafc;
    padding: 16px;
    border-radius: 8px;
}

.section-title {
    font-size: 15px;
    color: #4a6b8a;
    margin: 0 0 12px 0;
    font-weight: 500;
}

/* ===== Info Section ===== */
.info-section {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.info-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    padding: 24px;
}

.info-card h3 {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 20px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* ===== Simplified Document Management ===== */

/* Class Title */
.class-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 20px 0 12px 0;
    padding-bottom: 6px;
    border-bottom: 2px solid #3498db;
    display: inline-block;
}

/* Section Title */
.section-title {
    font-size: 1.05rem;
    font-weight: 500;
    color: #16a085;
    margin: 16px 0 10px 0;
    display: inline-block;
    background: #e8f5ff;
    padding: 4px 10px;
    border-radius: 6px;
}

/* Documents List */
.documents-list {
    list-style: none;
    margin: 8px 0 0;
    padding: 0;
}

/* Document Item */
.info-value a {
    display: block;
    padding: 10px 14px;
    color: #3498db;
    text-decoration: none;
    border-radius: 8px;
    transition: background 0.2s ease;
    font-size: 0.95rem;
    margin: 4px 0;
}

.info-value a:hover {
    background: #f0f7ff;
    color: #2980b9;
    font-weight: 500;
}

.info-value a::before {
    content: "📄";
    margin-right: 8px;
    font-size: 16px;
}

/* File Metadata */
.file-meta {
    color: #7f8c8d;
    font-size: 0.85rem;
    font-style: italic;
    margin-left: 24px;
    display: block;
    opacity: 0.9;
}

/* Empty State */
.no-docs {
    text-align: center;
    color: #95a5a6;
    font-style: italic;
    padding: 20px;
    background: #fdfdfd;
    border-radius: 8px;
    border: 1px dashed #ddd;
    margin-top: 10px;
    font-size: 0.95rem;
}

/* ===== Dialog Overlay ===== */
.dialog-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    z-index: 1000;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.dialog-overlay.show {
    opacity: 1;
}

.dialog-content {
    background: white;
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    text-align: center;
    max-width: 420px;
    margin: 20px;
    transform: scale(0.8) translateY(30px);
    transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    border: 1px solid #eee;
}

.dialog-overlay.show .dialog-content {
    transform: scale(1) translateY(0);
}

.dialog-content h3 {
    color: #2d3436;
    font-size: 26px;
    margin-bottom: 8px;
    font-weight: 600;
}

.dialog-content p {
    color: #636e72;
    font-size: 16px;
    margin-bottom: 30px;
    line-height: 1.6;
    opacity: 0.9;
}

.dialog-content::before {
    content: "⚠️";
    font-size: 48px;
    display: block;
    margin-bottom: 20px;
    opacity: 0.8;
}

.dialog-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-danger {
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 12px;
    padding: 14px 28px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    min-width: 140px;
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

.btn-danger:hover {
    background: #c0392b;
    transform: translateY(-2px);
}

.btn-cancel {
    background: #74b9ff;
    color: white;
    border: none;
    border-radius: 12px;
    padding: 14px 28px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    min-width: 140px;
    box-shadow: 0 4px 12px rgba(116, 185, 255, 0.3);
}

.btn-cancel:hover {
    background: #0984e3;
    transform: translateY(-2px);
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
    .container {
        padding: 16px;
    }

    .header-content {
        flex-direction: column;
        align-items: flex-start;
    }

    .main-content {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .upload-icon {
        font-size: 40px;
    }

    .upload-text {
        font-size: 15px;
    }

    .file-name {
        font-size: 14px;
    }

    .class-title {
        font-size: 1.15rem;
    }

    .section-title {
        font-size: 1rem;
    }

    .info-value a {
        padding: 9px 12px;
        font-size: 0.9rem;
    }

    .file-meta {
        margin-left: 22px;
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .header h1 {
        font-size: 20px;
    }

    .browse-btn {
        font-size: 14px;
        padding: 10px 20px;
    }

    .dialog-actions {
        flex-direction: column;
        gap: 10px;
    }

    .btn-danger,
    .btn-cancel {
        width: 100%;
        max-width: 220px;
    }
}