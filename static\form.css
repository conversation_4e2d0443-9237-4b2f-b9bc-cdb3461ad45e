:root {
    --primary-color: #4a90e2;
    --background-color: #f8f9fa;
    --surface-color: #ffffff;
    --text-primary: #333;
    --text-secondary: #777;
    --border-color: #dee2e6;
    --danger-color: #e74c3c;
}

.form-container {
    max-width: 700px;
    margin: 2rem auto;
    background-color: var(--surface-color);
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    overflow: hidden;
}

.form-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--border-color);
}

.form-header h1 {
    font-size: 1.5rem;
    margin: 0;
    color: var(--text-primary);
}

.form-body {
    padding: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.2);
}

textarea.form-control {
    min-height: 120px;
    resize: vertical;
}

.form-footer {
    padding: 1.5rem 2rem;
    background-color: var(--background-color);
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

.btn {
    padding: 0.6rem 1.2rem;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    border: 1px solid transparent;
    transition: all 0.2s ease;
    cursor: pointer;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}
.btn-primary:hover {
    opacity: 0.9;
}

.btn-secondary {
    background-color: var(--surface-color);
    color: var(--text-primary);
    border-color: var(--border-color);
}
.btn-secondary:hover {
    background-color: #e9ecef;
}
