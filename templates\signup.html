{% extends "base.html" %}

{% block title %}Inscription - Portail PDF Étudiant{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='signup.css') }}">
{% endblock %}

{% block content %}
<div class="signup-container">
    <h1>Créer un compte</h1>

    <form method="POST" autocomplete="off">
        <div class="form-row">
            <div class="form-group">
                <label for="first_name">Prénom *</label>
                <input type="text" id="first_name" name="first_name" placeholder="Votre prénom" required
                    autocomplete="off" maxlength="50">
            </div>

            <div class="form-group">
                <label for="last_name">Nom *</label>
                <input type="text" id="last_name" name="last_name" placeholder="Votre nom" required autocomplete="off"
                    maxlength="50">
            </div>
        </div>

        <div class="form-group">
            <label for="email">Adresse email *</label>
            <input type="email" id="email" name="email" placeholder="<EMAIL>" required autocomplete="off"
                maxlength="100">
        </div>
        <div class="form-group">
            <label for="classSelect">Classe</label>
            <select id="classSelect" name="class_id" required>
                <option value="">Sélectionner une classe</option>
                <option value="1">1er</option>
                <option value="2">2éme</option>
                <option value="3">3éme</option>
                <option value="4">Baccalauréat</option>
            </select>
        </div>

        <div class="form-group" id="sectionGroup">
            <label for="subjectSelect">Section</label>
            <select id="subjectSelect" name="section_id">
                <option value="">Sélectionner une Section</option>
                <option value="1">Informatique</option>
                <option value="2">Mathématiques</option>
                <option value="3">Sciences expérimentales</option>
                <option value="4">Economie & gestion</option>
                <option value="5">Technique</option>
            </select>
        </div>

        <div class="form-group">
            <label for="password">Mot de passe *</label>
            <div class="password-container">
                <input type="password" id="password" name="password" placeholder="Créez un mot de passe sécurisé"
                    required autocomplete="new-password" maxlength="200" minlength="8">
                <button type="button" class="password-toggle" onclick="togglePassword('password')">
                    👁️
                </button>
            </div>
            <small>Le mot de passe doit contenir au moins 8 caractères.</small>
        </div>

        <div class="form-group">
            <label for="confirm_password">Confirmer le mot de passe *</label>
            <div class="password-container">
                <input type="password" id="confirm_password" name="confirm_password"
                    placeholder="Confirmez votre mot de passe" required autocomplete="new-password" maxlength="200"
                    minlength="8">
                <button type="button" class="password-toggle" onclick="togglePassword('confirm_password')">
                    👁️
                </button>
            </div>
        </div>

        <button type="submit">Créer mon compte</button>

    </form>

    <div class="login-link">
        Vous avez déjà un compte? <a href="{{ url_for('login') }}">Connectez-vous</a>
    </div>
</div>

<script>
    function togglePassword(fieldId) {
        const field = document.getElementById(fieldId);
        const button = field.nextElementSibling;

        if (field.type === 'password') {
            field.type = 'text';
            button.textContent = '🙈';
        } else {
            field.type = 'password';
            button.textContent = '👁️';
        }
    }

    function updateSubjectVisibility(classId) {
        const subjectSelect = document.getElementById('subjectSelect');
        const subjectGroup = subjectSelect.closest('.form-group');

        // Handle 1er case - hide entire subject section
        if (classId === '' || classId === '1') {
            subjectGroup.style.display = 'none';
            subjectSelect.value = '';
            subjectSelect.removeAttribute('required');
            return;
        }

        // Show subject section for other classes
        subjectGroup.style.display = 'block';
        subjectSelect.setAttribute('required', 'required');

        const options = subjectSelect.querySelectorAll('option[value]:not([value=""])');
        options.forEach(option => {
            if (classId === '2' && option.value === '5') {
                option.style.display = 'none';
            } else {
                option.style.display = 'block';
            }
        });

        // Reset selection if currently selected option is now hidden
        const selectedOption = subjectSelect.options[subjectSelect.selectedIndex];
        if (selectedOption && selectedOption.style.display === 'none') {
            subjectSelect.value = '';
        }
    }

    // Add event listener
    document.getElementById('classSelect').addEventListener('change', function () {
        updateSubjectVisibility(this.value);
    });

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function () {
        const classSelect = document.getElementById('classSelect');
        updateSubjectVisibility(classSelect.value);
    });

</script>
{% endblock %}