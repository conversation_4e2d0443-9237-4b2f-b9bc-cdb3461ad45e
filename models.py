"""
Enhanced Database models for Flask application.
Simplified, professional, and developer-friendly user management system.
"""
from enum import Enum
from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timezone
from typing import Dict, Any, Optional
import uuid
db = SQLAlchemy()


class UserRole(Enum):
    """User role enumeration for type safety and clarity."""
    STUDENT = "student"
    TEACHER = "teacher"
    ADMIN = "admin"

    @property
    def display_name(self) -> str:
        """Human-readable role names."""
        role_names = {
            self.STUDENT: "Étudiant",
            self.TEACHER: "Enseignant",
            self.ADMIN: "Administrateur"
        }
        return role_names[self]


class User(UserMixin, db.Model):
    """
    Base User model with role-based access control.

    Uses single table inheritance for simplicity while maintaining
    role-specific functionality through the UserRole enum.
    """
    __tablename__ = 'users'

    # Primary fields
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)

    # Role and status
    role = db.Column(db.Enum(UserRole), nullable=False, default=UserRole.STUDENT)
    is_active = db.Column(db.Boolean, default=True, nullable=False, index=True)

    # Student-specific fields (nullable for non-students)
    student_class = db.Column(db.String(50), nullable=True)
    section = db.Column(db.String(20), nullable=True)

    # Timestamps
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc),
                          onupdate=lambda: datetime.now(timezone.utc))
    last_login = db.Column(db.DateTime, nullable=True)

    def __repr__(self) -> str:
        return f"<User {self.email} ({self.role.value})>"

    @property
    def full_name(self) -> str:
        """Get user's full name."""
        return f"{self.first_name} {self.last_name}"

    @property
    def display_role(self) -> str:
        """Get human-readable role name."""
        return self.role.display_name

    # Backward compatibility properties
    @property
    def name(self) -> str:
        """Get full name (backward compatibility)."""
        return self.full_name

    def get_full_name(self) -> str:
        """Get full name (backward compatibility)."""
        return self.full_name

    def get_user_type(self) -> str:
        """Get user type as string (backward compatibility)."""
        return self.role.value

    def get_role_display(self) -> str:
        """Get human-readable role name (backward compatibility)."""
        return self.display_role

    # Password management
    def set_password(self, password: str) -> None:
        """Set password with secure hashing."""
        if not password or len(password) < 6:
            raise ValueError("Password must be at least 6 characters long")
        self.password_hash = generate_password_hash(password)

    def check_password(self, password: str) -> bool:
        """Verify password against stored hash."""
        return check_password_hash(self.password_hash, password)

    # Role checking methods
    def is_student(self) -> bool:
        """Check if user is a student."""
        return self.role == UserRole.STUDENT and self.is_active

    def is_teacher(self) -> bool:
        """Check if user is a teacher."""
        return self.role == UserRole.TEACHER and self.is_active

    def is_admin(self) -> bool:
        """Check if user is an administrator."""
        return self.role == UserRole.ADMIN and self.is_active

    def has_role(self, role: UserRole) -> bool:
        """Check if user has specific role and is active."""
        return self.role == role and self.is_active

    # Permission methods
    def can_manage_users(self) -> bool:
        """Check if user can manage other users."""
        return self.is_admin()

    def can_manage_courses(self) -> bool:
        """Check if user can manage courses."""
        return self.is_admin() or self.is_teacher()

    def can_view_grades(self, student_id: Optional[int] = None) -> bool:
        """Check if user can view grades."""
        if self.is_admin() or self.is_teacher():
            return True
        if self.is_student() and student_id:
            return self.id == student_id
        return self.is_student()

    def can_access_admin(self) -> bool:
        """Check if user can access admin panel (backward compatibility)."""
        return self.is_admin()

    # Student-specific methods
    def set_class_info(self, student_class: str, section: str) -> None:
        """Set class information for students."""
        if not self.is_student():
            raise ValueError("Only students can have class information")
        self.student_class = student_class
        self.section = section

    @property
    def class_info(self) -> Optional[str]:
        """Get formatted class information for students."""
        return f"{self.student_class} - {self.section}"

    # Backward compatibility for Class attribute
    @property
    def Class(self) -> Optional[str]:
        """Get student class (backward compatibility)."""
        return self.student_class

    @Class.setter
    def Class(self, value: str) -> None:
        """Set student class (backward compatibility)."""
        self.student_class = value
    
    @classmethod
    def get_list_of_documents(cls):
        if cls.current_user().role not in ['teacher', 'admin']:
            return "Access denied. Only teachers and admins can view this page."
        docs=PDFDocument.query.all()
        documents = [doc.to_dict() for doc in docs]
        return documents
    # Data serialization
    def to_dict(self, include_sensitive: bool = False) -> Dict[str, Any]:
        """
        Convert user to dictionary format.

        Args:
            include_sensitive: Whether to include sensitive information
        """
        data = {
            'id': self.id,
            'email': self.email,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'full_name': self.full_name,
            'role': self.role.value,
            'display_role': self.display_role,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
        }

        # Add student-specific info
        if self.is_student():
            data.update({
                'student_class': self.student_class,
                'section': self.section,
                'class_info': self.class_info
            })

        # Add sensitive info if requested (for admin operations)
        if include_sensitive:
            data['last_login'] = self.last_login.isoformat() if self.last_login else None

        return data

    def data(self) -> Dict[str, Any]:
        """Get user data (backward compatibility)."""
        return {
            'id': self.id,
            'email': self.email,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'created_at': self.created_at,
            'is_active': self.is_active,
            'role': self.get_user_type(),
            'class': self.class_info
        }

    def update_last_login(self) -> None:
        """Update last login timestamp."""
        self.last_login = datetime.now(timezone.utc)

    @classmethod
    def create_user(cls, email: str, first_name: str, last_name: str,
                   password: str, role: UserRole = UserRole.STUDENT,
                   student_class: str = None, section: str = None) -> 'User':
        """
        Factory method to create a new user.

        Args:
            email: User's email address
            first_name: User's first name
            last_name: User's last name
            password: Plain text password
            role: User role (defaults to STUDENT)
            student_class: Class name (for students only)
            section: Section name (for students only)
        """
        user = cls(
            email=email.lower().strip(),
            first_name=first_name.strip(),
            last_name=last_name.strip(),
            role=role
        )

        user.set_password(password)

        # Set student-specific info if provided

        if role == UserRole.STUDENT:
            print(student_class, section)
            if student_class:
                user.student_class = student_class.strip()
            if section:
                user.section = section.strip()
        #print the user data

        return user

    @classmethod
    def find_by_email(cls, email: str) -> Optional['User']:
        """Find user by email address."""
        return cls.query.filter_by(email=email.lower().strip()).first()

    @classmethod
    def get_active_users_by_role(cls, role: UserRole):
        """Get all active users with specific role."""
        return cls.query.filter_by(role=role, is_active=True).all()


# Backward compatibility classes for existing code
class Student(User):
    """Backward compatibility class for Student."""
    def __new__(cls, *args, **kwargs):
        # Create a User instance with STUDENT role
        kwargs['role'] = UserRole.STUDENT
        return User(*args, **kwargs)


class Teacher(User):
    """Backward compatibility class for Teacher."""
    def __new__(cls, *args, **kwargs):
        # Create a User instance with TEACHER role
        kwargs['role'] = UserRole.TEACHER
        return User(*args, **kwargs)


class Admin(User):
    """Backward compatibility class for Admin."""
    def __new__(cls, *args, **kwargs):
        # Create a User instance with ADMIN role
        kwargs['role'] = UserRole.ADMIN
        return User(*args, **kwargs)


# Database event listeners for automatic timestamp updates
from sqlalchemy import event

@event.listens_for(User, 'before_update')
def update_timestamp(mapper, connection, target):
    """Automatically update the updated_at timestamp."""
    _ = mapper, connection  # Suppress unused parameter warnings
    target.updated_at = datetime.now(timezone.utc)




class PDFDocument(db.Model):
    __tablename__ = 'pdf_documents'

    id = db.Column(
        db.String(36),  # Standard length for UUID as string
        primary_key=True,
        default=lambda: str(uuid.uuid4())
    )
    title = db.Column(db.String(200), nullable=False)
    filename = db.Column(db.String(255), nullable=False)
    class_level = db.Column(db.String(50), nullable=False)
    section = db.Column(db.String(100), nullable=True)
    
    # Foreign key to User (teacher)
    teacher_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    teacher = db.relationship('User', backref=db.backref('pdfs_uploaded', lazy=True))

    # Timestamps
    uploaded_at = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))

    # Optional: file size, description, etc.
    file_size = db.Column(db.Integer)  # in bytes
    description = db.Column(db.Text, nullable=True)
    file_url = db.Column(db.String(255), nullable=False)   

    def __repr__(self):
        return f"<PDF: {self.title} by {self.teacher.full_name}>"

    def to_dict(self):
        return {
            'id': self.id,
            'title': self.title,
            'filename': self.filename,
            'class_level': self.class_level,
            'section': self.section,
            'teacher': self.teacher.full_name,
            'teacher_id': self.teacher.id,
            'uploaded_at': self.uploaded_at.isoformat(),
            'file_size': f"{self.file_size // 1024} KB" if self.file_size else None,
            'description': self.description
        }
    
    def data(self):
        return {
            'id': self.id,
            'title': self.title,
            'filename': self.filename,
            'class_level': self.class_level,
            'section': self.section,
            'teacher': self.teacher.full_name,
            'teacher_id': self.teacher.id,
            'uploaded_at': self.uploaded_at.isoformat(),
            'file_size': f"{self.file_size // 1024} KB" if self.file_size else None,
            'description': self.description
        }