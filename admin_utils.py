"""
Admin utilities and security decorators for Flask application.
"""
from functools import wraps
from flask import abort, request, session, current_app
from flask_login import current_user, login_required
import time
import hashlib
import secrets


def admin_required(f):
    """
    Decorator to require admin access for routes.
    Provides additional security checks beyond basic authentication.
    """
    @wraps(f)
    @login_required
    def decorated_function(*args, **kwargs):
        # Check if user is authenticated and has admin role
        if not current_user.is_authenticated:
            abort(401)
        
        if not current_user.can_access_admin():
            current_app.logger.warning(
                f"Unauthorized admin access attempt by user {current_user.email} "
                f"from IP {request.remote_addr}"
            )
            abort(403)
        
        # Additional security: Check session integrity
        if not _verify_admin_session():
            current_app.logger.warning(
                f"Invalid admin session for user {current_user.email} "
                f"from IP {request.remote_addr}"
            )
            abort(403)
        
        return f(*args, **kwargs)
    return decorated_function


def _verify_admin_session():
    """
    Verify admin session integrity with additional security checks.
    """
    if not current_user.is_authenticated:
        return False
    
    # Check if admin session token exists and is valid
    admin_token = session.get('admin_token')
    if not admin_token:
        return False
    
    # Verify token matches expected pattern
    expected_token = _generate_admin_token(current_user.id)
    return admin_token == expected_token


def _generate_admin_token(user_id):
    """
    Generate a secure admin session token.
    """
    # Create a token based on user ID, session secret, and current hour
    # This ensures tokens expire every hour for additional security
    current_hour = int(time.time() // 3600)
    token_data = f"{user_id}:{current_app.secret_key}:{current_hour}"
    return hashlib.sha256(token_data.encode()).hexdigest()


def create_admin_session():
    """
    Create a secure admin session for the current user.
    """
    if current_user.is_authenticated and current_user.can_access_admin():
        session['admin_token'] = _generate_admin_token(current_user.id)
        session['admin_login_time'] = time.time()
        return True
    return False


def destroy_admin_session():
    """
    Destroy admin session data.
    """
    session.pop('admin_token', None)
    session.pop('admin_login_time', None)


def generate_csrf_token():
    """
    Generate a CSRF token for forms.
    """
    if 'csrf_token' not in session:
        session['csrf_token'] = secrets.token_hex(16)
    return session['csrf_token']


def verify_csrf_token(token):
    """
    Verify CSRF token.
    """
    return token and session.get('csrf_token') == token


def rate_limit_check(key, limit=5, window=300):
    """
    Simple rate limiting check.
    Returns True if request should be allowed, False if rate limited.
    """
    # This is a simple in-memory rate limiter
    # In production, you'd want to use Redis or similar
    if not hasattr(current_app, '_rate_limits'):
        current_app._rate_limits = {}
    
    now = time.time()
    window_start = now - window
    
    # Clean old entries
    current_app._rate_limits = {
        k: [t for t in v if t > window_start]
        for k, v in current_app._rate_limits.items()
    }
    
    # Check current key
    if key not in current_app._rate_limits:
        current_app._rate_limits[key] = []
    
    if len(current_app._rate_limits[key]) >= limit:
        return False
    
    current_app._rate_limits[key].append(now)
    return True
