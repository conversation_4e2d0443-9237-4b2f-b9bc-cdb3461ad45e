import os
import sys
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app
from models import db

def create_new_database():
    print("🔄 Création de la nouvelle base de données...")
    
    with app.app_context():
        try:
            # Delete existing database
            db_uri = app.config['SQLALCHEMY_DATABASE_URI']
            if db_uri.startswith('sqlite:///'):
                db_path = db_uri.replace('sqlite:///', '')
                if os.path.exists(db_path):
                    os.remove(db_path)
                    print("🗑️  Base de données supprimée")
            
            # Create new database with all tables
            db.create_all()
            print("✅ Base de données créée avec succès !")
            return True
            
        except Exception as e:
            print(f"❌ Erreur : {e}")
            return False

if __name__ == '__main__':
    if create_new_database():
        print("\n🎉 Prêt ! Exécutez : python create_admin.py")
    else:
        print("\n❌ Échec de la création.")